//
//  SyncErrorHandler.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/21.
//

import Foundation
import CloudKit
import CoreData

/// 同步错误处理器
class SyncErrorHandler {
    static let shared = SyncErrorHandler()

    private init() {}

    // MARK: - Error Types

    enum SyncError: LocalizedError {
        case accountNotAvailable
        case networkUnavailable
        case quotaExceeded
        case recordConflict(String)
        case unknownError(Error)

        var errorDescription: String? {
            switch self {
            case .accountNotAvailable:
                return "iCloud账户不可用。请检查您的iCloud登录状态。"
            case .networkUnavailable:
                return "网络连接不可用。请检查您的网络连接。"
            case .quotaExceeded:
                return "iCloud存储空间不足。请清理iCloud存储空间或升级存储计划。"
            case .recordConflict(let recordType):
                return "数据同步冲突：\(recordType)。系统将自动解决冲突。"
            case .unknownError(let error):
                return "同步过程中发生未知错误：\(error.localizedDescription)"
            }
        }

        var recoverySuggestion: String? {
            switch self {
            case .accountNotAvailable:
                return "请前往设置 > Apple ID > iCloud，确保已登录并启用iCloud Drive。"
            case .networkUnavailable:
                return "请检查Wi-Fi或蜂窝网络连接，然后重试。"
            case .quotaExceeded:
                return "请前往设置 > Apple ID > iCloud > 管理存储空间，清理不需要的数据或购买更多存储空间。"
            case .recordConflict:
                return "系统将自动选择最新的数据版本。如有问题，请联系技术支持。"
            case .unknownError:
                return "请重启应用或联系技术支持。"
            }
        }
    }

    // MARK: - Public Methods

    /// 处理CloudKit错误
    func handleCloudKitError(_ error: Error) -> SyncError {
        if let ckError = error as? CKError {
            return handleCKError(ckError)
        } else {
            return .unknownError(error)
        }
    }

    /// 处理Core Data错误
    func handleCoreDataError(_ error: Error) -> SyncError {
        if let nsError = error as NSError? {
            switch nsError.code {
            case NSManagedObjectMergeError:
                return .recordConflict("Core Data对象")
            default:
                return .unknownError(error)
            }
        }
        return .unknownError(error)
    }

    /// 检查错误是否可以重试
    func isRetryableError(_ error: SyncError) -> Bool {
        switch error {
        case .networkUnavailable, .unknownError:
            return true
        case .accountNotAvailable, .quotaExceeded, .recordConflict:
            return false
        }
    }

    /// 获取重试延迟时间（秒）
    func getRetryDelay(for error: SyncError, attempt: Int) -> TimeInterval {
        guard isRetryableError(error) else { return 0 }

        // 指数退避策略
        let baseDelay: TimeInterval = 2.0
        let maxDelay: TimeInterval = 60.0
        let delay = min(baseDelay * pow(2.0, Double(attempt)), maxDelay)

        return delay
    }
}

// MARK: - Private Methods
private extension SyncErrorHandler {

    /// 处理具体的CKError
    func handleCKError(_ ckError: CKError) -> SyncError {
        switch ckError.code {
        case .notAuthenticated:
            return .accountNotAvailable
        case .networkUnavailable, .networkFailure:
            return .networkUnavailable
        case .quotaExceeded:
            return .quotaExceeded
        case .serverRecordChanged:
            return .recordConflict("CloudKit记录")
        case .zoneNotFound, .userDeletedZone:
            return .recordConflict("CloudKit区域")
        case .changeTokenExpired:
            return .recordConflict("同步令牌")
        case .requestRateLimited:
            return .networkUnavailable // 可以重试
        case .zoneBusy:
            return .networkUnavailable // 可以重试
        default:
            return .unknownError(ckError)
        }
    }
}