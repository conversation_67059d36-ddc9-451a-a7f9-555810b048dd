//
//  AnalysisReportService.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import Foundation

/// 长期数据分析结构
struct LongTermAnalysisData {
    let totalRecords: Int
    let recordingDays: Int
    let averageRecordsPerDay: Double
    let commonSymptoms: [SymptomFrequency]
    let monthlyTrends: [MonthlyTrend]
    let riskIndicators: [RiskIndicator]
    let timePatterns: TimePatterns
}



/// 月度趋势
struct MonthlyTrend {
    let month: String
    let recordCount: Int
    let mainSymptoms: [String]
    let severity: String // "轻微", "中等", "严重"
}

/// 风险指标
struct RiskIndicator {
    let type: String // "慢性病风险", "生活习惯", "症状持续性"
    let level: String // "低", "中", "高"
    let description: String
    let evidence: [String]
}

/// 时间模式分析
struct TimePatterns {
    let peakHours: [Int] // 症状高发时段
    let peakDays: [String] // 症状高发日期
    let seasonalPattern: String // 季节性模式
}

/// 分析报告服务，负责数据处理和报告生成
class AnalysisReportService {
    
    /// 单例实例
    static let shared = AnalysisReportService()
    
    private init() {}
    
    /// 生成分析报告
    /// - Parameters:
    ///   - dataViewModel: 数据视图模型
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    /// - Returns: 生成的分析报告内容
    @MainActor
    func generateAnalysisReport(
        dataViewModel: DataViewModel,
        startDate: Date,
        endDate: Date
    ) async throws -> String {

        // 1. 获取用户信息
        guard let userInfo = dataViewModel.userInfo else {
            throw AnalysisError.missingUserInfo
        }

        // 2. 检查API调用次数
        guard userInfo.remainingCalls > 0 else {
            throw AnalysisError.insufficientAPICalls
        }

        // 3. 获取时间范围内的记录
        let records = dataViewModel.getRecords(from: startDate, to: endDate)

        guard !records.isEmpty else {
            throw AnalysisError.noRecordsFound
        }

        // 4. 获取长期历史数据用于趋势分析
        let longTermData = getLongTermAnalysisData(dataViewModel: dataViewModel, currentEndDate: endDate)

        // 5. 构建增强的prompt
        let prompt = buildEnhancedPrompt(
            userInfo: userInfo,
            records: records,
            startDate: startDate,
            endDate: endDate,
            longTermData: longTermData
        )
        
        // 5. 调用AI生成报告
        let reportContent: String
        do {
            reportContent = try await DeepSeekService.shared.generateAnalysisReport(prompt: prompt)
        } catch {
            // 将DeepSeek API错误转换为用户友好的错误
            if let apiError = error as? DeepSeekService.APIError {
                switch apiError {
                case .networkError(let message):
                    throw AnalysisError.networkError(message)
                case .apiError(let message):
                    throw AnalysisError.apiError(message)
                default:
                    throw AnalysisError.reportGenerationFailed
                }
            } else {
                throw AnalysisError.reportGenerationFailed
            }
        }

        // 6. 消费API调用次数
        let success = await dataViewModel.consumeApiCall()
        if !success {
            print("⚠️ 警告: API调用次数消费失败")
        }

        // 7. 保存报告到数据库
        let timeRange = formatDateRange(startDate: startDate, endDate: endDate)
        await dataViewModel.addReport(
            title: "健康分析报告 - \(timeRange)",
            type: .analysis,
            content: reportContent,
            timeRange: timeRange
        )

        return reportContent
    }

    // MARK: - Long-term Analysis Methods

    /// 获取长期分析数据
    @MainActor
    private func getLongTermAnalysisData(dataViewModel: DataViewModel, currentEndDate: Date) -> LongTermAnalysisData {
        // 获取过去6个月的数据
        let calendar = Calendar.current
        let sixMonthsAgo = calendar.date(byAdding: .month, value: -6, to: currentEndDate) ?? currentEndDate
        let allRecords = dataViewModel.getRecords(from: sixMonthsAgo, to: currentEndDate)

        // 计算基础统计
        let totalRecords = allRecords.count
        let recordDates = Set<Date>(allRecords.compactMap { record in
            guard let timestamp = record.timestamp else { return nil }
            return calendar.startOfDay(for: timestamp)
        })
        let recordingDays = recordDates.count
        let averageRecordsPerDay = recordingDays > 0 ? Double(totalRecords) / Double(recordingDays) : 0

        // 分析症状频率
        let commonSymptoms = analyzeSymptomFrequency(records: allRecords)

        // 分析月度趋势
        let monthlyTrends = analyzeMonthlyTrends(records: allRecords)

        // 评估风险指标
        let riskIndicators = assessRiskIndicators(records: allRecords, userAge: dataViewModel.userInfo?.age ?? 0)

        // 分析时间模式
        let timePatterns = analyzeTimePatterns(records: allRecords)

        return LongTermAnalysisData(
            totalRecords: totalRecords,
            recordingDays: recordingDays,
            averageRecordsPerDay: averageRecordsPerDay,
            commonSymptoms: commonSymptoms,
            monthlyTrends: monthlyTrends,
            riskIndicators: riskIndicators,
            timePatterns: timePatterns
        )
    }

    /// 分析症状频率
    private func analyzeSymptomFrequency(records: [RecordEntity]) -> [SymptomFrequency] {
        var symptomCounts: [String: Int] = [:]
        let totalRecords = records.count

        // 定义常见症状关键词
        let symptomKeywords = [
            "头痛", "头晕", "胃痛", "腹痛", "腹泻", "便秘", "恶心", "呕吐",
            "发烧", "咳嗽", "感冒", "疲劳", "失眠", "焦虑", "压力", "心悸",
            "胸闷", "背痛", "腰痛", "关节痛", "肌肉痛", "过敏", "皮疹",
            "眼干", "眼痛", "视力", "耳鸣", "喉咙痛", "鼻塞", "流鼻涕"
        ]

        // 统计症状出现次数
        for record in records {
            let text = record.text?.lowercased() ?? ""
            for keyword in symptomKeywords {
                if text.contains(keyword) {
                    symptomCounts[keyword, default: 0] += 1
                }
            }
        }

        // 转换为SymptomFrequency并排序
        let filteredSymptoms = symptomCounts.filter { $0.value >= 2 } // 至少出现2次
        let mappedSymptoms = filteredSymptoms.map { (symptom, count) in
            let percentage = totalRecords > 0 ? Double(count) / Double(totalRecords) * 100 : 0
            let trend = determineTrend(symptom: symptom, records: records)
            return SymptomFrequency(
                symptom: symptom,
                count: count,
                percentage: percentage,
                trend: trend
            )
        }
        let sortedSymptoms = mappedSymptoms.sorted { $0.count > $1.count }
        return Array(sortedSymptoms.prefix(10)) // 取前10个最常见症状
    }

    /// 确定症状趋势
    private func determineTrend(symptom: String, records: [RecordEntity]) -> String {
        let calendar = Calendar.current
        let now = Date()
        let oneMonthAgo = calendar.date(byAdding: .month, value: -1, to: now) ?? now
        let twoMonthsAgo = calendar.date(byAdding: .month, value: -2, to: now) ?? now

        let recentCount = records.filter { record in
            guard let timestamp = record.timestamp else { return false }
            return timestamp >= oneMonthAgo && (record.text?.contains(symptom) ?? false)
        }.count

        let previousCount = records.filter { record in
            guard let timestamp = record.timestamp else { return false }
            return timestamp >= twoMonthsAgo && timestamp < oneMonthAgo && (record.text?.contains(symptom) ?? false)
        }.count

        if recentCount > previousCount {
            return "增加"
        } else if recentCount < previousCount {
            return "减少"
        } else {
            return "稳定"
        }
    }

    /// 分析月度趋势
    private func analyzeMonthlyTrends(records: [RecordEntity]) -> [MonthlyTrend] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy年MM月"
        dateFormatter.locale = Locale(identifier: "zh_CN")

        var monthlyData: [String: [RecordEntity]] = [:]

        // 按月分组记录
        for record in records {
            guard let timestamp = record.timestamp else { continue }
            let monthKey = dateFormatter.string(from: timestamp)
            monthlyData[monthKey, default: []].append(record)
        }

        // 分析每月数据
        return monthlyData.map { (month, monthRecords) in
            let recordCount = monthRecords.count
            let mainSymptoms = extractMainSymptoms(from: monthRecords)
            let severity = assessSeverity(records: monthRecords)

            return MonthlyTrend(
                month: month,
                recordCount: recordCount,
                mainSymptoms: mainSymptoms,
                severity: severity
            )
        }.sorted { $0.month > $1.month } // 按时间倒序
    }

    /// 提取主要症状
    private func extractMainSymptoms(from records: [RecordEntity]) -> [String] {
        let symptomKeywords = ["头痛", "胃痛", "腹痛", "疲劳", "失眠", "焦虑", "发烧", "咳嗽"]
        var symptomCounts: [String: Int] = [:]

        for record in records {
            let text = record.text?.lowercased() ?? ""
            for keyword in symptomKeywords {
                if text.contains(keyword) {
                    symptomCounts[keyword, default: 0] += 1
                }
            }
        }

        return symptomCounts
            .sorted { $0.value > $1.value }
            .prefix(3)
            .map { $0.key }
    }

    /// 评估严重程度
    private func assessSeverity(records: [RecordEntity]) -> String {
        let severityKeywords = [
            "严重": 3, "剧烈": 3, "无法": 3, "急性": 3,
            "中等": 2, "明显": 2, "持续": 2,
            "轻微": 1, "偶尔": 1, "轻度": 1
        ]

        var totalScore = 0
        var recordCount = 0

        for record in records {
            let text = record.text?.lowercased() ?? ""
            var recordScore = 1 // 默认轻微

            for (keyword, score) in severityKeywords {
                if text.contains(keyword) {
                    recordScore = max(recordScore, score)
                }
            }

            totalScore += recordScore
            recordCount += 1
        }

        let averageScore = recordCount > 0 ? Double(totalScore) / Double(recordCount) : 1.0

        if averageScore >= 2.5 {
            return "严重"
        } else if averageScore >= 1.5 {
            return "中等"
        } else {
            return "轻微"
        }
    }

    /// 评估风险指标
    private func assessRiskIndicators(records: [RecordEntity], userAge: Int) -> [RiskIndicator] {
        var indicators: [RiskIndicator] = []

        // 慢性病风险评估
        let chronicDiseaseRisk = assessChronicDiseaseRisk(records: records, userAge: userAge)
        if let risk = chronicDiseaseRisk {
            indicators.append(risk)
        }

        // 生活习惯风险评估
        let lifestyleRisk = assessLifestyleRisk(records: records)
        if let risk = lifestyleRisk {
            indicators.append(risk)
        }

        // 症状持续性风险评估
        let persistenceRisk = assessSymptomPersistence(records: records)
        if let risk = persistenceRisk {
            indicators.append(risk)
        }

        return indicators
    }

    /// 评估慢性病风险
    private func assessChronicDiseaseRisk(records: [RecordEntity], userAge: Int) -> RiskIndicator? {
        let chronicSymptoms = [
            "高血压": ["头晕", "头痛", "心悸", "胸闷"],
            "糖尿病": ["多饮", "多尿", "疲劳", "视力模糊"],
            "胃病": ["胃痛", "腹痛", "恶心", "消化不良", "反酸"],
            "心血管疾病": ["胸痛", "心悸", "气短", "胸闷"],
            "关节炎": ["关节痛", "关节僵硬", "肌肉痛"]
        ]

        var riskScores: [String: Int] = [:]
        var evidence: [String] = []

        for record in records {
            let text = record.text?.lowercased() ?? ""
            for (disease, symptoms) in chronicSymptoms {
                for symptom in symptoms {
                    if text.contains(symptom) {
                        riskScores[disease, default: 0] += 1
                        if !evidence.contains(symptom) {
                            evidence.append(symptom)
                        }
                    }
                }
            }
        }

        // 找出最高风险的疾病
        guard let (topDisease, score) = riskScores.max(by: { $0.value < $1.value }) else {
            return nil
        }

        // 根据年龄和症状频率确定风险等级
        let ageRiskFactor = userAge > 40 ? 1.2 : 1.0
        let adjustedScore = Double(score) * ageRiskFactor

        let level: String
        let description: String

        if adjustedScore >= 8 {
            level = "高"
            description = "检测到\(topDisease)相关症状频繁出现，建议尽快就医检查"
        } else if adjustedScore >= 4 {
            level = "中"
            description = "检测到\(topDisease)相关症状，建议关注并定期体检"
        } else if adjustedScore >= 2 {
            level = "低"
            description = "偶有\(topDisease)相关症状，建议保持健康生活方式"
        } else {
            return nil
        }

        return RiskIndicator(
            type: "慢性病风险",
            level: level,
            description: description,
            evidence: evidence.prefix(5).map { $0 }
        )
    }

    /// 评估生活习惯风险
    private func assessLifestyleRisk(records: [RecordEntity]) -> RiskIndicator? {
        let lifestyleKeywords = [
            "熬夜", "失眠", "睡眠不足", "压力大", "焦虑", "抽烟", "喝酒",
            "暴饮暴食", "不规律", "久坐", "缺乏运动", "外卖", "快餐"
        ]

        var riskCount = 0
        var evidence: [String] = []

        for record in records {
            let text = record.text?.lowercased() ?? ""
            for keyword in lifestyleKeywords {
                if text.contains(keyword) {
                    riskCount += 1
                    if !evidence.contains(keyword) {
                        evidence.append(keyword)
                    }
                }
            }
        }

        guard riskCount > 0 else { return nil }

        let level: String
        let description: String

        if riskCount >= 10 {
            level = "高"
            description = "生活习惯存在多项问题，急需改善"
        } else if riskCount >= 5 {
            level = "中"
            description = "生活习惯有待改善，建议逐步调整"
        } else {
            level = "低"
            description = "生活习惯基本良好，注意保持"
        }

        return RiskIndicator(
            type: "生活习惯",
            level: level,
            description: description,
            evidence: evidence.prefix(5).map { $0 }
        )
    }

    /// 评估症状持续性风险
    private func assessSymptomPersistence(records: [RecordEntity]) -> RiskIndicator? {
        let calendar = Calendar.current
        let now = Date()
        let oneWeekAgo = calendar.date(byAdding: .weekOfYear, value: -1, to: now) ?? now
        let twoWeeksAgo = calendar.date(byAdding: .weekOfYear, value: -2, to: now) ?? now

        let recentRecords = records.filter { record in
            guard let timestamp = record.timestamp else { return false }
            return timestamp >= oneWeekAgo
        }

        let previousRecords = records.filter { record in
            guard let timestamp = record.timestamp else { return false }
            return timestamp >= twoWeeksAgo && timestamp < oneWeekAgo
        }

        // 检查持续出现的症状
        let recentSymptoms = Set(recentRecords.compactMap { $0.text })
        let previousSymptoms = Set(previousRecords.compactMap { $0.text })
        let persistentSymptoms = recentSymptoms.intersection(previousSymptoms)

        guard !persistentSymptoms.isEmpty else { return nil }

        let level: String
        let description: String

        if persistentSymptoms.count >= 3 {
            level = "高"
            description = "多种症状持续出现，建议及时就医"
        } else if persistentSymptoms.count >= 2 {
            level = "中"
            description = "部分症状持续出现，需要关注"
        } else {
            level = "低"
            description = "个别症状持续，建议观察"
        }

        return RiskIndicator(
            type: "症状持续性",
            level: level,
            description: description,
            evidence: Array(persistentSymptoms.prefix(3))
        )
    }

    /// 分析时间模式
    private func analyzeTimePatterns(records: [RecordEntity]) -> TimePatterns {
        let calendar = Calendar.current
        var hourCounts: [Int: Int] = [:]
        var dayCounts: [String: Int] = [:]

        let dayFormatter = DateFormatter()
        dayFormatter.dateFormat = "EEEE"
        dayFormatter.locale = Locale(identifier: "zh_CN")

        for record in records {
            guard let timestamp = record.timestamp else { continue }

            // 统计小时分布
            let hour = calendar.component(.hour, from: timestamp)
            hourCounts[hour, default: 0] += 1

            // 统计星期分布
            let dayName = dayFormatter.string(from: timestamp)
            dayCounts[dayName, default: 0] += 1
        }

        // 找出高发时段（前3个小时）
        let peakHours = hourCounts
            .sorted { $0.value > $1.value }
            .prefix(3)
            .map { $0.key }
            .sorted()

        // 找出高发日期（前3天）
        let peakDays = dayCounts
            .sorted { $0.value > $1.value }
            .prefix(3)
            .map { $0.key }

        // 简单的季节性分析
        let seasonalPattern = analyzeSeasonalPattern(records: records)

        return TimePatterns(
            peakHours: peakHours,
            peakDays: peakDays,
            seasonalPattern: seasonalPattern
        )
    }

    /// 分析季节性模式
    private func analyzeSeasonalPattern(records: [RecordEntity]) -> String {
        let calendar = Calendar.current
        var seasonCounts: [String: Int] = [:]

        for record in records {
            guard let timestamp = record.timestamp else { continue }
            let month = calendar.component(.month, from: timestamp)

            let season: String
            switch month {
            case 3, 4, 5:
                season = "春季"
            case 6, 7, 8:
                season = "夏季"
            case 9, 10, 11:
                season = "秋季"
            default:
                season = "冬季"
            }

            seasonCounts[season, default: 0] += 1
        }

        guard let (peakSeason, _) = seasonCounts.max(by: { $0.value < $1.value }) else {
            return "无明显季节性模式"
        }

        return "\(peakSeason)症状较多"
    }

    /// 构建增强的AI分析prompt（包含长期数据）
    private func buildEnhancedPrompt(
        userInfo: UserInfoEntity,
        records: [RecordEntity],
        startDate: Date,
        endDate: Date,
        longTermData: LongTermAnalysisData
    ) -> String {

        // 读取prompt模板
        guard let promptTemplate = loadPromptTemplate() else {
            return buildEnhancedFallbackPrompt(userInfo: userInfo, records: records, startDate: startDate, endDate: endDate, longTermData: longTermData)
        }

        // 格式化用户信息
        let gender = userInfo.userGender?.displayName ?? "未设置"
        let age = userInfo.age

        // 格式化时间范围
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.locale = Locale(identifier: "zh_CN")

        let startDateString = dateFormatter.string(from: startDate)
        let endDateString = dateFormatter.string(from: endDate)

        // 格式化记录
        let recordsString = formatRecords(records)

        // 格式化长期数据分析
        let longTermAnalysis = formatLongTermAnalysis(longTermData)

        // 替换模板中的占位符
        return promptTemplate
            .replacingOccurrences(of: "{{gender}}", with: gender)
            .replacingOccurrences(of: "{{age}}", with: "\(age)")
            .replacingOccurrences(of: "{{start_date}}", with: startDateString)
            .replacingOccurrences(of: "{{end_date}}", with: endDateString)
            .replacingOccurrences(of: "{{records}}", with: recordsString)
            .replacingOccurrences(of: "{{long_term_analysis}}", with: longTermAnalysis)
    }

    /// 格式化长期数据分析
    private func formatLongTermAnalysis(_ data: LongTermAnalysisData) -> String {
        var analysis = """

        ### 长期健康数据分析（过去6个月）

        **基础统计：**
        - 总记录数：\(data.totalRecords) 条
        - 记录天数：\(data.recordingDays) 天
        - 平均每日记录：\(String(format: "%.1f", data.averageRecordsPerDay)) 条

        """

        // 常见症状分析
        if !data.commonSymptoms.isEmpty {
            analysis += """
            **常见症状频率：**

            """
            for symptom in data.commonSymptoms.prefix(5) {
                analysis += "- \(symptom.symptom)：出现 \(symptom.count) 次（\(String(format: "%.1f", symptom.percentage))%），趋势：\(symptom.trend)\n"
            }
            analysis += "\n"
        }

        // 月度趋势
        if !data.monthlyTrends.isEmpty {
            analysis += """
            **月度趋势：**

            """
            for trend in data.monthlyTrends.prefix(3) {
                analysis += "- \(trend.month)：\(trend.recordCount) 条记录，主要症状：\(trend.mainSymptoms.joined(separator: "、"))，严重程度：\(trend.severity)\n"
            }
            analysis += "\n"
        }

        // 风险指标
        if !data.riskIndicators.isEmpty {
            analysis += """
            **风险评估：**

            """
            for risk in data.riskIndicators {
                analysis += "- \(risk.type)：\(risk.level)风险 - \(risk.description)\n"
                if !risk.evidence.isEmpty {
                    analysis += "  相关症状：\(risk.evidence.joined(separator: "、"))\n"
                }
            }
            analysis += "\n"
        }

        // 时间模式
        analysis += """
        **时间模式分析：**
        - 症状高发时段：\(data.timePatterns.peakHours.map { "\($0):00" }.joined(separator: "、"))
        - 症状高发日期：\(data.timePatterns.peakDays.joined(separator: "、"))
        - 季节性模式：\(data.timePatterns.seasonalPattern)

        """

        return analysis
    }

    /// 构建AI分析prompt
    private func buildPrompt(
        userInfo: UserInfoEntity,
        records: [RecordEntity],
        startDate: Date,
        endDate: Date
    ) -> String {
        
        // 读取prompt模板
        guard let promptTemplate = loadPromptTemplate() else {
            return buildFallbackPrompt(userInfo: userInfo, records: records, startDate: startDate, endDate: endDate)
        }
        
        // 格式化用户信息
        let gender = userInfo.userGender?.displayName ?? "未设置"
        let age = userInfo.age
        
        // 格式化时间范围
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.locale = Locale(identifier: "zh_CN")
        
        let startDateString = dateFormatter.string(from: startDate)
        let endDateString = dateFormatter.string(from: endDate)
        
        // 格式化记录
        let recordsString = formatRecords(records)
        
        // 替换模板中的占位符
        return promptTemplate
            .replacingOccurrences(of: "{{gender}}", with: gender)
            .replacingOccurrences(of: "{{age}}", with: "\(age)")
            .replacingOccurrences(of: "{{start_date}}", with: startDateString)
            .replacingOccurrences(of: "{{end_date}}", with: endDateString)
            .replacingOccurrences(of: "{{records}}", with: recordsString)
    }
    
    /// 加载prompt模板
    private func loadPromptTemplate() -> String? {
        guard let path = Bundle.main.path(forResource: "分析报告prompt", ofType: "md"),
              let content = try? String(contentsOfFile: path, encoding: .utf8) else {
            print("⚠️ 警告: 无法加载分析报告prompt模板")
            return nil
        }
        return content
    }
    
    /// 格式化记录为文本
    private func formatRecords(_ records: [RecordEntity]) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .short
        dateFormatter.timeStyle = .short
        dateFormatter.locale = Locale(identifier: "zh_CN")
        
        return records
            .sorted { ($0.timestamp ?? Date.distantPast) < ($1.timestamp ?? Date.distantPast) }
            .map { record in
                let timestamp = record.timestamp ?? Date()
                let timeString = dateFormatter.string(from: timestamp)
                let content = record.text ?? ""
                return "[\(timeString)] \(content)"
            }
            .joined(separator: "\n")
    }
    
    /// 格式化日期范围
    private func formatDateRange(startDate: Date, endDate: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        
        let startString = formatter.string(from: startDate)
        let endString = formatter.string(from: endDate)
        
        return "\(startString) ~ \(endString)"
    }

    /// 增强的备用prompt构建（包含长期数据分析）
    private func buildEnhancedFallbackPrompt(
        userInfo: UserInfoEntity,
        records: [RecordEntity],
        startDate: Date,
        endDate: Date,
        longTermData: LongTermAnalysisData
    ) -> String {
        let gender = userInfo.userGender?.displayName ?? "未设置"
        let age = userInfo.age
        let timeRange = formatDateRange(startDate: startDate, endDate: endDate)
        let recordsString = formatRecords(records)
        let longTermAnalysis = formatLongTermAnalysis(longTermData)

        return """
        你是一名专业的智能健康顾问，拥有临床数据分析和健康趋势解读的能力。请根据以下用户信息和身体记录生成一份科学、专业且易理解的健康分析报告。

        ## 用户信息
        - 性别：\(gender)
        - 年龄：\(age) 岁

        ## 当前分析时间范围
        \(timeRange)

        ## 当前时间段记录
        \(recordsString)

        \(longTermAnalysis)

        ## 分析要求

        请基于以上数据生成包含以下内容的健康分析报告：

        ### 1. 概览总结
        - 简要概述用户的主要健康状况和记录特征
        - 结合长期数据分析用户的整体健康趋势

        ### 2. 症状趋势分析
        - 识别用户常见不适的频率变化和发展趋势
        - 分析症状的时间模式和季节性特征
        - 对比短期和长期数据，指出变化趋势

        ### 3. 慢性病风险评估
        - 基于症状模式和频率，评估潜在的慢性病风险
        - 结合用户年龄和症状持续性进行风险分级
        - 提供具体的风险指标和证据

        ### 4. 生活方式分析
        - 根据记录分析饮食、作息、运动等生活习惯
        - 识别可能影响健康的不良生活方式
        - 评估生活习惯对症状的影响

        ### 5. 健康建议与改善方案
        - 提供个性化的健康改善建议
        - 针对识别的风险提供预防措施
        - 如有必要，建议就医检查的科室和时机

        ### 6. 长期健康管理建议
        - 基于长期数据趋势提供健康管理策略
        - 建议定期监测的健康指标
        - 提供生活方式调整的具体方案

        ## 注意事项
        - 使用简洁清晰的格式，避免复杂的表格和特殊符号
        - 避免使用粗体标记，直接使用清晰的文字表达
        - 内容分段清晰，每段不超过3行
        - 基于循证医学知识，避免虚假保证或确诊结论
        - 对严重症状或异常趋势，明确建议就医
        """
    }

    /// 备用prompt构建（当模板文件不可用时）
    private func buildFallbackPrompt(
        userInfo: UserInfoEntity,
        records: [RecordEntity],
        startDate: Date,
        endDate: Date
    ) -> String {
        let gender = userInfo.userGender?.displayName ?? "未设置"
        let age = userInfo.age
        let timeRange = formatDateRange(startDate: startDate, endDate: endDate)
        let recordsString = formatRecords(records)
        
        return """
        你是一名专业的智能健康顾问，请根据以下用户信息和身体记录生成健康分析报告：
        
        用户信息：
        - 性别：\(gender)
        - 年龄：\(age) 岁
        
        分析时间范围：\(timeRange)
        
        用户记录：
        \(recordsString)
        
        请生成包含以下内容的分析报告：
        1. 概览总结
        2. 症状趋势分析
        3. 可能的风险和原因
        4. 饮食与生活方式分析
        5. 建议与改善方案
        
        请使用Markdown格式输出，内容要科学、专业且易于理解。
        """
    }
}

/// 分析报告错误类型
enum AnalysisError: Error, LocalizedError {
    case missingUserInfo
    case insufficientAPICalls
    case noRecordsFound
    case reportGenerationFailed
    case networkError(String)
    case apiError(String)

    var errorDescription: String? {
        switch self {
        case .missingUserInfo:
            return "用户信息不完整，请先完善个人信息"
        case .insufficientAPICalls:
            return "API调用次数不足，请购买更多次数"
        case .noRecordsFound:
            return "选择的时间范围内没有找到记录"
        case .reportGenerationFailed:
            return "报告生成失败，请稍后重试"
        case .networkError(let message):
            return "网络错误: \(message)"
        case .apiError(let message):
            return "API错误: \(message)"
        }
    }
}
