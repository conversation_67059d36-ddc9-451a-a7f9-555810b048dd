//
//  RevenueCatConfig.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/22.
//

import Foundation

/// RevenueCat配置管理
struct RevenueCatConfig {
    
    // MARK: - API Keys
    /// RevenueCat API Key (从RevenueCat控制台获取)
    static let apiKey = "appl_gIXEDqHdJAwthkWrlMfwZTvuKQM"
    
    // MARK: - Product Identifiers
    /// 产品标识符
    struct ProductIdentifiers {
        /// 20次调用产品ID
        static let twentyCalls = "bodylog_20_calls"
        /// 50次调用产品ID
        static let fiftyCalls = "bodylog_50_calls"
    }
    
    // MARK: - Offering Identifiers
    /// Offering标识符
    struct OfferingIdentifiers {
        /// AI调用次数Offering
        static let aiCalls = "ai_calls"
    }
    
    // MARK: - Package Identifiers
    /// Package标识符
    struct PackageIdentifiers {
        /// 20次调用包
        static let twentyCalls = "twenty_calls"
        /// 50次调用包
        static let fiftyCalls = "fifty_calls"
    }
    
    // MARK: - Product Configuration
    /// 产品配置信息
    struct ProductConfig {
        let productId: String
        let callCount: Int
        let displayName: String
        let description: String
    }
    
    /// 所有产品配置
    static let allProducts: [ProductConfig] = [
        ProductConfig(
            productId: ProductIdentifiers.twentyCalls,
            callCount: 20,
            displayName: "20次",
            description: "调用AI次数"
        ),
        ProductConfig(
            productId: ProductIdentifiers.fiftyCalls,
            callCount: 50,
            displayName: "50次",
            description: "调用AI次数"
        )
    ]
    
    // MARK: - Helper Methods
    
    /// 根据产品ID获取调用次数
    static func getCallCount(for productId: String) -> Int {
        return allProducts.first { $0.productId == productId }?.callCount ?? 0
    }
    
    /// 根据产品ID获取显示名称
    static func getDisplayName(for productId: String) -> String {
        return allProducts.first { $0.productId == productId }?.displayName ?? "未知产品"
    }
    
    /// 验证API Key是否已配置
    static var isAPIKeyConfigured: Bool {
        return !apiKey.isEmpty && apiKey != "YOUR_REVENUECAT_API_KEY"
    }
}

// MARK: - Environment Configuration
extension RevenueCatConfig {
    
    /// 环境配置
    enum Environment {
        case development
        case production
        
        /// 当前环境
        static var current: Environment {
            #if DEBUG
            return .development
            #else
            return .production
            #endif
        }
        
        /// 是否为开发环境
        var isDevelopment: Bool {
            return self == .development
        }
    }
}
