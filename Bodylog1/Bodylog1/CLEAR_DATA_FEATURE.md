# 清除所有数据功能实现文档

## 功能概述

为iOS应用"身体日记"实现了完整的"清除所有数据"功能，该功能位于个人中心页面，允许用户安全地清除所有本地和iCloud同步的数据。

## 功能特性

### ✅ 已实现的功能

1. **数据清除范围**
   - ✅ CoreData中存储的所有用户记录数据（身体日记记录）
   - ✅ 用户个人信息（性别、年龄等基本信息）
   - ✅ 历史生成的分析报告和身体档案
   - ✅ 所有提醒设置
   - ✅ 本地和iCloud同步的数据

2. **用户体验**
   - ✅ 确认对话框，防止用户误操作
   - ✅ 详细说明将要删除的数据内容和不可恢复性
   - ✅ "取消"和"确认清除"两个选项
   - ✅ 清除完成后显示成功提示
   - ✅ 清除过程中显示加载指示器

3. **技术实现**
   - ✅ 使用DataViewModel中的方法来清除数据
   - ✅ 数据清除操作的原子性（要么全部成功，要么全部失败）
   - ✅ 完整的错误处理和用户提示
   - ✅ 兼容iOS 16.6+系统

4. **安全考虑**
   - ✅ 数据清除操作不可逆
   - ✅ 用户不会重新获得免费调用次数

## 代码实现

### 1. DataViewModel 清除方法

```swift
/// 清除所有数据（包括本地和iCloud数据）
func clearAllData() async throws {
    return try await withCheckedThrowingContinuation { continuation in
        context.perform {
            do {
                // 1. 删除所有记录
                let recordRequest: NSFetchRequest<RecordEntity> = RecordEntity.fetchRequest()
                let allRecords = try self.context.fetch(recordRequest)
                allRecords.forEach { self.context.delete($0) }
                
                // 2. 删除所有报告
                let reportRequest: NSFetchRequest<ReportEntity> = ReportEntity.fetchRequest()
                let allReports = try self.context.fetch(reportRequest)
                allReports.forEach { self.context.delete($0) }
                
                // 3. 删除所有提醒
                let reminderRequest: NSFetchRequest<ReminderEntity> = ReminderEntity.fetchRequest()
                let allReminders = try self.context.fetch(reminderRequest)
                allReminders.forEach { self.context.delete($0) }
                
                // 4. 重置用户信息（保持实体但重置数据，不重新获得免费调用次数）
                if let userInfo = self.userInfo {
                    userInfo.gender = UserInfoEntity.Gender.notSet.rawValue
                    userInfo.birthDate = Date()
                    // 注意：不重置 remainingCalls，确保用户不会重新获得免费调用次数
                }
                
                // 5. 保存更改
                try self.context.save()
                
                continuation.resume()
            } catch {
                continuation.resume(throwing: error)
            }
        }
    }
}
```

### 2. UI 交互实现

```swift
// 状态变量
@State private var showClearDataConfirmation = false
@State private var isClearingData = false
@State private var clearDataError: String?

// 按钮实现
ProfileSettingRow(icon: "删除账号", title: "清除所有数据", isFirst: false, isLast: true) {
    if !isClearingData {
        showClearDataConfirmation = true
    }
}

// 确认对话框
.alert("确认清除所有数据", isPresented: $showClearDataConfirmation) {
    Button("取消", role: .cancel) { }
    Button("确认清除", role: .destructive) {
        Task {
            await clearAllData()
        }
    }
} message: {
    Text("此操作将永久删除以下数据，且无法恢复：\n\n• 所有身体记录\n• 所有分析报告和身体档案\n• 个人信息设置\n• 所有提醒设置\n• 本地和iCloud同步数据\n\n请确认是否继续？")
}
```

### 3. 清除数据方法

```swift
private func clearAllData() async {
    isClearingData = true
    clearDataError = nil
    
    do {
        try await dataViewModel.clearAllData()
        
        // 清除成功后刷新数据
        await dataViewModel.refreshData()
        
        // 显示成功提示
        DispatchQueue.main.async {
            self.isClearingData = false
            self.showToast(message: "所有数据已成功清除", type: .success)
        }
    } catch {
        DispatchQueue.main.async {
            self.isClearingData = false
            self.clearDataError = "清除数据失败：\(error.localizedDescription)"
        }
    }
}
```

## 使用方法

1. 打开应用，进入个人中心页面
2. 滚动到底部，找到"清除所有数据"选项
3. 点击该选项，会弹出确认对话框
4. 仔细阅读将要删除的数据说明
5. 点击"确认清除"执行操作，或点击"取消"放弃操作
6. 清除过程中会显示加载指示器
7. 清除完成后会显示成功提示

## 安全特性

- **不可逆操作**：数据一旦清除无法恢复
- **详细确认**：明确告知用户将要删除的数据类型
- **防误操作**：需要用户明确确认才能执行
- **原子性**：要么全部清除成功，要么全部失败
- **保护机制**：不会重新获得免费调用次数

## 测试

项目包含了完整的测试代码 `ClearDataTests.swift`，可以验证功能的正确性。

## 兼容性

- ✅ iOS 16.6+
- ✅ iCloud 同步
- ✅ CoreData + CloudKit
- ✅ 多设备同步清除

## 测试验证

### 手动测试步骤

1. **准备测试数据**
   - 添加几条身体记录
   - 生成一份分析报告
   - 设置个人信息（性别、年龄）
   - 创建提醒设置

2. **执行清除操作**
   - 进入个人中心页面
   - 滚动到底部找到"清除所有数据"
   - 点击该选项
   - 仔细阅读确认对话框内容
   - 点击"确认清除"

3. **验证清除结果**
   - 检查历史记录页面是否为空
   - 检查个人信息是否重置
   - 检查提醒设置是否清空
   - 验证API调用次数是否保持不变

### 错误测试

1. **网络异常测试**
   - 在清除过程中断开网络
   - 验证错误处理是否正常

2. **取消操作测试**
   - 点击"清除所有数据"
   - 在确认对话框中点击"取消"
   - 验证数据是否保持不变

## 故障排除

### 常见问题

1. **清除操作失败**
   - 检查设备存储空间
   - 重启应用后重试
   - 检查iCloud同步状态

2. **部分数据未清除**
   - 这种情况不应该发生（原子性操作）
   - 如果发生，请重新执行清除操作

3. **清除后应用崩溃**
   - 重启应用
   - 数据应该已经被正确清除

## 注意事项

1. 此操作不可逆，请谨慎使用
2. 清除数据后，用户的API调用次数不会重置
3. 清除操作会同时影响本地和iCloud数据
4. 建议在重要数据清除前提醒用户备份
5. 清除操作需要网络连接以同步iCloud数据
