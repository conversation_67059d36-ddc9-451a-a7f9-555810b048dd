//
//  ContentViewIntegration.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import SwiftUI
import CoreData

/// 展示如何将新的数据模型集成到现有 ContentView 中
struct ContentViewIntegrationExample: View {
    @StateObject private var dataViewModel = DataViewModel()
    @StateObject private var speechRecognitionManager = SpeechRecognitionManager()
    @State private var selectedTab = 1
    
    var body: some View {
        ZStack {
            // 背景渐变
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 226/255, green: 255/255, blue: 232/255),
                    Color.white
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()

            VStack(spacing: 0) {
                // 主要内容区域
                TabView(selection: $selectedTab) {
                    // 历史记录页面 - 使用真实数据
                    HistoryViewWithData(dataViewModel: dataViewModel)
                        .tag(0)

                    // 记录页面 - 使用真实数据
                    RecordViewWithData(dataViewModel: dataViewModel)
                        .tag(1)

                    // 个人中心页面 - 使用真实数据
                    ProfileViewWithData(dataViewModel: dataViewModel)
                        .tag(2)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))

                // 自定义TabBar
                CustomTabBar(selectedTab: $selectedTab, speechRecognitionManager: speechRecognitionManager)
            }
        }
        .onAppear {
            // 应用启动时加载数据
            Task {
                await dataViewModel.refreshData()
            }
        }
    }
}

// MARK: - 历史记录页面（集成数据模型）
struct HistoryViewWithData: View {
    @ObservedObject var dataViewModel: DataViewModel
    @State private var selectedDate = Date()
    @State private var currentMonth = Date()
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题
            HStack {
                Text("历史记录")
                    .font(.title2)
                    .fontWeight(.bold)
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            // 日历视图
            CalendarViewWithData(
                selectedDate: $selectedDate,
                currentMonth: $currentMonth,
                recordDates: dataViewModel.recordDates
            )
            .padding(.horizontal, 20)
            
            // 当日记录列表
            DayRecordsView(
                selectedDate: selectedDate,
                dataViewModel: dataViewModel
            )
            
            // 操作按钮
            ActionButtonsView(
                selectedDate: selectedDate,
                dataViewModel: dataViewModel
            )
        }
        .onChange(of: selectedDate) { _ in
            // 当选择日期改变时，可以进行额外的操作
        }
    }
}

// MARK: - 日历视图（使用真实数据）
struct CalendarViewWithData: View {
    @Binding var selectedDate: Date
    @Binding var currentMonth: Date
    let recordDates: Set<Date>
    
    private let calendar = Calendar.current
    
    var body: some View {
        VStack(spacing: 16) {
            // 月份导航
            HStack {
                Button(action: { changeMonth(by: -1) }) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.primary)
                }
                
                Spacer()
                
                Text(monthYearString)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button(action: { changeMonth(by: 1) }) {
                    Image(systemName: "chevron.right")
                        .foregroundColor(.primary)
                }
            }
            .padding(.horizontal)
            
            // 星期标题
            HStack {
                ForEach(["日", "一", "二", "三", "四", "五", "六"], id: \.self) { weekday in
                    Text(weekday)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity)
                }
            }
            
            // 日期网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                ForEach(getDaysInMonth(), id: \.self) { date in
                    if let date = date {
                        DayViewWithData(
                            date: date,
                            isSelected: calendar.isDate(date, inSameDayAs: selectedDate),
                            hasRecord: recordDates.contains(calendar.startOfDay(for: date)),
                            isCurrentMonth: calendar.isDate(date, equalTo: currentMonth, toGranularity: .month)
                        ) {
                            selectedDate = date
                        }
                    } else {
                        Color.clear
                            .frame(height: 40)
                    }
                }
            }
        }
    }
    
    private var monthYearString: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "yyyy年M月"
        return formatter.string(from: currentMonth)
    }
    
    private func changeMonth(by offset: Int) {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentMonth = calendar.date(byAdding: .month, value: offset, to: currentMonth) ?? currentMonth
        }
    }
    
    private func getDaysInMonth() -> [Date?] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentMonth) else {
            return []
        }
        
        let firstOfMonth = monthInterval.start
        let firstWeekday = calendar.component(.weekday, from: firstOfMonth)
        let numberOfDaysInMonth = calendar.range(of: .day, in: .month, for: currentMonth)?.count ?? 0
        
        var days: [Date?] = []
        
        // 添加前面的空白日期
        for _ in 1..<firstWeekday {
            days.append(nil)
        }
        
        // 添加当月的日期
        for day in 1...numberOfDaysInMonth {
            if let date = calendar.date(byAdding: .day, value: day - 1, to: firstOfMonth) {
                days.append(date)
            }
        }
        
        return days
    }
}

// MARK: - 日期视图（使用真实数据）
struct DayViewWithData: View {
    let date: Date
    let isSelected: Bool
    let hasRecord: Bool
    let isCurrentMonth: Bool
    let action: () -> Void
    
    private let calendar = Calendar.current
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 2) {
                Text("\(calendar.component(.day, from: date))")
                    .font(.system(size: 16, weight: isSelected ? .semibold : .regular))
                    .foregroundColor(textColor)
                
                // 记录指示点
                Circle()
                    .fill(hasRecord ? Color.green : Color.clear)
                    .frame(width: 4, height: 4)
            }
            .frame(width: 40, height: 40)
            .background(backgroundColor)
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var textColor: Color {
        if !isCurrentMonth {
            return .secondary
        } else if isSelected {
            return .white
        } else {
            return .primary
        }
    }
    
    private var backgroundColor: Color {
        if isSelected {
            return Color.blue
        } else {
            return Color.clear
        }
    }
}

// MARK: - 当日记录视图
struct DayRecordsView: View {
    let selectedDate: Date
    @ObservedObject var dataViewModel: DataViewModel
    
    var dayRecords: [RecordEntity] {
        dataViewModel.getRecords(for: selectedDate)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(dateString)
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text("共 \(dayRecords.count) 条记录")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 20)
            
            if dayRecords.isEmpty {
                Text("这一天还没有记录")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(dayRecords, id: \.objectID) { record in
                            RecordCardView(record: record, dataViewModel: dataViewModel)
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
        }
    }
    
    private var dateString: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "M月d日"
        return formatter.string(from: selectedDate)
    }
}

// MARK: - 记录卡片视图
struct RecordCardView: View {
    let record: RecordEntity
    @ObservedObject var dataViewModel: DataViewModel
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                Text(record.text ?? "")
                    .font(.body)
                    .lineLimit(nil)
                
                Text(record.formattedTime)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button("删除") {
                Task {
                    await dataViewModel.deleteRecord(record)
                }
            }
            .font(.caption)
            .foregroundColor(.red)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - 操作按钮视图
struct ActionButtonsView: View {
    let selectedDate: Date
    @ObservedObject var dataViewModel: DataViewModel
    
    var body: some View {
        HStack(spacing: 16) {
            Button("生成分析报告") {
                generateAnalysisReport()
            }
            .buttonStyle(ActionButtonStyle(color: .blue))
            
            Button("生成身体档案") {
                generateBodyArchive()
            }
            .buttonStyle(ActionButtonStyle(color: .green))
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }
    
    private func generateAnalysisReport() {
        Task {
            // 检查记录数量是否达到15条
            let totalRecords = dataViewModel.records.count
            if totalRecords < 15 {
                print("记录的数量不足15条，不能调用AI，记录数量越多，分析结果越准确。关注身体变化，坚持每天记录吧！")
                return
            }

            // 检查是否有足够的调用次数
            let canCall = await dataViewModel.consumeApiCall()
            if canCall {
                // 这里调用 AI API 生成报告
                // 示例：生成一个测试报告
                await dataViewModel.addReport(
                    title: "健康分析报告",
                    type: .analysis,
                    content: "基于您的记录，建议注意休息...",
                    timeRange: "最近一周"
                )
            } else {
                // 显示调用次数不足的提示
                print("调用次数不足")
            }
        }
    }
    
    private func generateBodyArchive() {
        Task {
            // 检查记录数量是否达到15条
            let totalRecords = dataViewModel.records.count
            if totalRecords < 15 {
                print("记录的数量不足15条，不能调用AI，记录数量越多，分析结果越准确。关注身体变化，坚持每天记录吧！")
                return
            }

            let canCall = await dataViewModel.consumeApiCall()
            if canCall {
                await dataViewModel.addReport(
                    title: "身体档案",
                    type: .archive,
                    content: "您的身体状况档案...",
                    timeRange: "最近一个月"
                )
            } else {
                print("调用次数不足")
            }
        }
    }
}

// MARK: - 按钮样式
struct ActionButtonStyle: ButtonStyle {
    let color: Color
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(color)
            .cornerRadius(25)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - 记录页面（集成数据模型）
struct RecordViewWithData: View {
    @ObservedObject var dataViewModel: DataViewModel
    @State private var recordText = ""
    @State private var isTextEditorFocused = false
    
    var body: some View {
        VStack(spacing: 20) {
            // 标题和统计
            VStack(spacing: 8) {
                Text("记录身体状态")
                    .font(.title2)
                    .fontWeight(.bold)
                
                if let userInfo = dataViewModel.userInfo {
                    Text("剩余调用次数: \(userInfo.remainingCalls)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 输入区域
            VStack(alignment: .leading, spacing: 8) {
                Text("今天的身体状况如何？")
                    .font(.headline)
                
                TextEditor(text: $recordText)
                    .frame(minHeight: 120)
                    .padding(12)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isTextEditorFocused ? Color.blue : Color.clear, lineWidth: 2)
                    )
            }
            .padding(.horizontal, 20)
            
            // 保存按钮
            Button("保存记录") {
                saveRecord()
            }
            .buttonStyle(ActionButtonStyle(color: .blue))
            .disabled(recordText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            
            // 统计信息
            if !dataViewModel.records.isEmpty {
                VStack(spacing: 8) {
                    Text("已记录 \(dataViewModel.records.count) 条")
                        .font(.headline)
                    
                    Text("坚持记录，关注健康")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
        }
        .onTapGesture {
            isTextEditorFocused = false
            hideKeyboard()
        }
    }
    
    private func saveRecord() {
        let text = recordText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !text.isEmpty else { return }
        
        Task {
            try await dataViewModel.addRecord(text: text, timestamp: Date())
            recordText = ""
            isTextEditorFocused = false
        }
    }
}

// MARK: - 个人中心页面（集成数据模型）
struct ProfileViewWithData: View {
    @ObservedObject var dataViewModel: DataViewModel
    
    var body: some View {
        VStack(spacing: 20) {
            Text("个人中心")
                .font(.title2)
                .fontWeight(.bold)
            
            // 用户信息
            if let userInfo = dataViewModel.userInfo {
                UserInfoSection(userInfo: userInfo, dataViewModel: dataViewModel)
            }
            
            // 统计信息
            StatisticsSection(dataViewModel: dataViewModel)
            
            // 历史报告
            ReportsSection(dataViewModel: dataViewModel)
            
            Spacer()
        }
        .padding(.horizontal, 20)
    }
}

// MARK: - 用户信息区域
struct UserInfoSection: View {
    let userInfo: UserInfoEntity
    @ObservedObject var dataViewModel: DataViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("用户信息")
                .font(.headline)
            
            HStack {
                Text("性别: \(userInfo.userGender?.displayName ?? "未设置")")
                Spacer()
                Text("年龄: \(userInfo.age)岁")
            }
            
            HStack {
                Text("剩余调用: \(userInfo.remainingCalls)次")
                Spacer()
                Button("购买更多") {
                    // 这里实现内购逻辑
                    Task {
                        await dataViewModel.addApiCalls(100)
                    }
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 统计信息区域
struct StatisticsSection: View {
    @ObservedObject var dataViewModel: DataViewModel

    var body: some View {
        let stats = dataViewModel.getStatistics()

        StatisticsCard(
            totalRecords: stats.totalRecords,
            totalReports: stats.totalReports,
            activeReminders: stats.activeReminders
        )
    }
}

// MARK: - 报告区域
struct ReportsSection: View {
    @ObservedObject var dataViewModel: DataViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("历史报告")
                .font(.headline)
            
            if dataViewModel.reports.isEmpty {
                Text("暂无报告")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(Array(dataViewModel.reports.prefix(5)), id: \.objectID) { report in
                            ReportRowView(report: report)
                        }
                    }
                }
                .frame(maxHeight: 200)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct ReportRowView: View {
    let report: ReportEntity
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(report.title ?? "")
                    .font(.body)
                    .fontWeight(.medium)
                
                Text(report.formattedCreatedAt)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(report.reportType?.displayName ?? "")
                .font(.caption)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.blue.opacity(0.2))
                .cornerRadius(8)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

// MARK: - 辅助扩展
// hideKeyboard 扩展已在 ContentView.swift 中定义，这里不重复定义

// MARK: - 自定义 TabBar（重用现有的）
// 这里可以重用你现有的 CustomTabBar 实现
