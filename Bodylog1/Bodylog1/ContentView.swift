//
//  ContentView.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/19.
//

import SwiftUI
import CoreData

// MARK: - 屏幕适配扩展
extension UIScreen {
    static var isSmallDevice: Bool {
        main.bounds.height < 700
    }

    static var isCompactWidth: Bool {
        main.bounds.width < 375
    }
}

struct ContentView: View {
    @StateObject private var dataViewModel = DataViewModel()
    @StateObject private var keyboardManager = KeyboardManager()
    @StateObject private var speechRecognitionManager = SpeechRecognitionManager()
    @StateObject private var userAgreementManager = UserAgreementManager.shared
    @EnvironmentObject var notificationManager: NotificationManager
    @EnvironmentObject var cloudKitManager: CloudKitManager
    @State private var selectedTab = 1 // 默认选中记录页面
    @State private var showUserAgreementPopup = false

    var body: some View {
        ZStack {
            // 背景渐变
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 226/255, green: 255/255, blue: 232/255),
                    Color.white
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()

            VStack(spacing: 0) {
                // 主要内容区域
                TabView(selection: $selectedTab) {
                    HistoryView(dataViewModel: dataViewModel, keyboardManager: keyboardManager)
                        .tag(0)

                    RecordView(dataViewModel: dataViewModel, keyboardManager: keyboardManager, speechRecognitionManager: speechRecognitionManager)
                        .tag(1)

                    ProfileView(dataViewModel: dataViewModel, keyboardManager: keyboardManager)
                        .tag(2)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))

                // 自定义TabBar
                CustomTabBar(selectedTab: $selectedTab, speechRecognitionManager: speechRecognitionManager)
            }
        }
        .keyboardAdaptive(keyboardManager: keyboardManager)
        .onAppear {
            // 应用启动时加载数据
            Task {
                await dataViewModel.refreshData()
                // 同步提醒到通知系统
                await dataViewModel.syncRemindersToNotifications(notificationManager: notificationManager)
            }

            // 检查是否需要显示用户协议弹窗
            checkAndShowUserAgreementPopup()
        }
        // 用户协议弹窗覆盖层
        .overlay(
            ZStack {
                if showUserAgreementPopup {
                    UserAgreementPopupView(
                        isPresented: $showUserAgreementPopup,
                        onAgree: {
                            userAgreementManager.agreeToTerms()
                            showUserAgreementPopup = false
                        },
                        onDisagree: {
                            userAgreementManager.disagreeToTerms()
                        }
                    )
                    .transition(.opacity)
                    .zIndex(1000) // 确保弹窗在最顶层
                }
            }
            .animation(.easeInOut(duration: 0.3), value: showUserAgreementPopup)
        )
    }

    // MARK: - Private Methods

    /// 检查并显示用户协议弹窗
    private func checkAndShowUserAgreementPopup() {
        // 延迟一小段时间确保界面已经加载完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if userAgreementManager.shouldShowAgreementPopup() {
                showUserAgreementPopup = true
            }
        }
    }
}

// 历史记录页面
struct HistoryView: View {
    @ObservedObject var dataViewModel: DataViewModel
    @ObservedObject var keyboardManager: KeyboardManager
    @State private var selectedDate = Date()
    @State private var currentMonth = Date()
    @State private var showingRecordDetail = false
    @State private var selectedRecord: RecordEntity?
    @State private var editingText = ""
    @State private var isAnimating = false
    @State private var animationDirection: AnimationDirection = .none
    @State private var showingDeleteConfirmation = false
    @State private var recordToDelete: RecordEntity?

    // 分析报告相关状态
    @State private var showingDateRangePicker = false
    @State private var reportStartDate = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
    @State private var reportEndDate = Date()
    @State private var showingAnalysisReport = false
    @State private var analysisReportContent = ""
    @State private var analysisTimeRange = ""
    @State private var isGeneratingReport = false
    @State private var showingReportError = false
    @State private var reportErrorMessage = ""
    @State private var showingInsufficientCallsAlert = false
    @State private var showPurchasePage = false

    // 身体档案相关状态
    @State private var showingArchiveDateRangePicker = false
    @State private var archiveStartDate = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
    @State private var archiveEndDate = Date()
    @State private var showingBodyArchive = false
    @State private var bodyArchiveContent = ""
    @State private var archiveTimeRange = ""
    @State private var isGeneratingArchive = false

    // 使用真实数据
    var todayRecords: [RecordEntity] {
        dataViewModel.getRecords(for: selectedDate)
    }

    var recordDates: Set<Date> {
        dataViewModel.recordDates
    }

    var body: some View {
        VStack(spacing: 0) {
            // 日历区域
            VStack(spacing: 45) {
                // 日历头部 - 年月显示和翻页按钮
                HStack {
                    Text(monthYearString(from: currentMonth))
                        .font(.custom("PingFang SC", size: 18))
                        .fontWeight(.semibold)
                        .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))

                    Spacer()

                    HStack(spacing: 16) {
                        Button(action: {
                            changeMonth(by: -1)
                        }) {
                            Image(systemName: "chevron.left")
                                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                                .font(.system(size: 16, weight: .medium))
                        }
                        .disabled(isAnimating)

                        Button(action: {
                            changeMonth(by: 1)
                        }) {
                            Image(systemName: "chevron.right")
                                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                                .font(.system(size: 16, weight: .medium))
                        }
                        .disabled(isAnimating)
                    }
                }
                .padding(.horizontal, 28)
                .padding(.top, 4)

                // 带动画的日历视图 - 固定高度
                AnimatedCalendarGridView(
                    currentMonth: currentMonth,
                    selectedDate: $selectedDate,
                    recordDates: getRecordDates(),
                    animationDirection: animationDirection,
                    isAnimating: isAnimating,
                    onMonthChange: { monthOffset in
                        changeMonth(by: monthOffset)
                    }
                )
                .frame(height: 240) // 固定高度防止移位
                .padding(.horizontal, 24)
                .padding(.bottom, 16)
            }
            .background(Color.clear) // 透明背景

            // 历史记录列表
            VStack(spacing: 0) {
                // 历史记录标题
                HStack {
                    Text("历史记录")
                        .font(.custom("PingFang SC", size: 20))
                        .fontWeight(.bold)
                        .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))
                    Spacer()

                    // 添加记录数量指示
                    if !getRecordsForSelectedDate().isEmpty {
                        Text("\(getRecordsForSelectedDate().count)条记录")
                            .font(.custom("PingFang SC", size: 14))
                            .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                            .padding(.horizontal, 12)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.1))
                            )
                    }
                }
                .padding(.horizontal, 28)
                .padding(.top, 20)
                .padding(.bottom, 12)

                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(getRecordsForSelectedDate()) { record in
                            EnhancedHistoryRecordRow(record: record) {
                                selectedRecord = record
                                editingText = record.text ?? ""
                                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                    showingRecordDetail = true
                                }
                            }
                        }

                        // 空状态提示
                        if getRecordsForSelectedDate().isEmpty {
                            VStack(spacing: 12) {
                                Image(systemName: "calendar.badge.plus")
                                    .font(.system(size: 32))
                                    .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))

                                Text("这一天还没有记录")
                                    .font(.custom("PingFang SC", size: 16))
                                    .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))

                                Text("你可以在记录页面进行补记录")
                                    .font(.custom("PingFang SC", size: 14))
                                    .foregroundColor(Color(red: 187/255, green: 187/255, blue: 187/255))
                            }
                            .padding(.vertical, 40)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 8)
                }
                .frame(maxHeight: UIScreen.isSmallDevice ? 180 : 220)

                // 底部按钮 - 美化设计
                HStack(spacing: 16) {
                    EnhancedActionButton(
                        title: "生成分析报告",
                        icon: "chart.bar.doc.horizontal",
                        style: .outline
                    ) {
                        showingDateRangePicker = true
                    }

                    EnhancedActionButton(
                        title: "生成身体档案",
                        icon: "person.text.rectangle",
                        style: .filled
                    ) {
                        showingArchiveDateRangePicker = true
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 28)
            }
        }
        .offset(y: 0) // 整个页面向下偏移50像素
        // 自定义弹窗覆盖层
        .overlay(
            ZStack {
                if showingRecordDetail {
                    // 背景遮罩层
                    Color.black.opacity(0.4)
                        .ignoresSafeArea(.all)
                        .onTapGesture {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showingRecordDetail = false
                            }
                        }
                        .transition(.opacity)

                    // 弹窗内容
                    if let record = selectedRecord {
                        VStack {
                            Spacer()
                            CustomRecordDetailView(
                                record: record,
                                editingText: $editingText,
                                isPresented: $showingRecordDetail,
                                onSave: { updatedText in
                                    updateRecord(record, with: updatedText)
                                },
                                onDelete: {
                                    recordToDelete = record
                                    showingDeleteConfirmation = true
                                },
                                keyboardManager: keyboardManager
                            )
                            Spacer()
                        }
                        .transition(.asymmetric(
                            insertion: .scale(scale: 0.8).combined(with: .opacity),
                            removal: .scale(scale: 0.8).combined(with: .opacity)
                        ))
                    }
                }
            }
            .animation(.easeInOut(duration: 0.3), value: showingRecordDetail)
        )
        // 删除确认对话框
        .alert("确认删除", isPresented: $showingDeleteConfirmation) {
            Button("取消", role: .cancel) {
                recordToDelete = nil
            }
            Button("删除", role: .destructive) {
                if let record = recordToDelete {
                    deleteRecord(record)
                    recordToDelete = nil
                    showingRecordDetail = false
                }
            }
        } message: {
            Text("确定要删除这条记录吗？删除后无法恢复。")
        }
        // 时间范围选择弹窗
        .overlay {
            if showingDateRangePicker {
                DateRangePickerView(
                    isPresented: $showingDateRangePicker,
                    startDate: $reportStartDate,
                    endDate: $reportEndDate,
                    onConfirm: {
                        generateAnalysisReport()
                    }
                )
            }
        }
        // 身体档案时间范围选择弹窗
        .overlay {
            if showingArchiveDateRangePicker {
                DateRangePickerView(
                    isPresented: $showingArchiveDateRangePicker,
                    startDate: $archiveStartDate,
                    endDate: $archiveEndDate,
                    onConfirm: {
                        generateBodyArchive()
                    }
                )
            }
        }
        // 分析报告显示
        .fullScreenCover(isPresented: $showingAnalysisReport) {
            AnalysisReportView(
                reportContent: analysisReportContent,
                timeRange: analysisTimeRange
            )
        }
        // 身体档案显示
        .fullScreenCover(isPresented: $showingBodyArchive) {
            BodyArchiveView(
                archiveContent: bodyArchiveContent,
                timeRange: archiveTimeRange,
                records: dataViewModel.getRecords(from: archiveStartDate, to: archiveEndDate)
            )
        }
        // 报告生成提示
        .alert("提示", isPresented: $showingReportError) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(reportErrorMessage)
        }
        // 调用次数不足提示
        .alert("调用次数不足", isPresented: $showingInsufficientCallsAlert) {
            Button("取消", role: .cancel) { }
            Button("前往购买") {
                showPurchasePage = true
            }
        } message: {
            Text("您的AI调用次数不足，无法生成报告。请购买更多次数后再试。")
        }
        // 购买页面
        .sheet(isPresented: $showPurchasePage) {
            PurchaseCountView(dataViewModel: dataViewModel)
        }
        // 加载指示器
        .overlay {
            if isGeneratingReport || isGeneratingArchive {
                ZStack {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()

                    VStack(spacing: 16) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(1.2)

                        Text(isGeneratingReport ? "正在生成分析报告...\n可能需要等待1-2分钟，请耐心等待" : "正在生成身体档案...\n可能需要等待1-2分钟，请耐心等待")
                            .font(.custom("PingFang SC", size: 16))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 32)
                    .padding(.vertical, 24)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.black.opacity(0.8))
                    )
                }
                .transition(.opacity)
                .animation(.easeInOut(duration: 0.3), value: isGeneratingReport || isGeneratingArchive)
            }
        }
    }

    private func monthYearString(from date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "yyyy年M月"
        return formatter.string(from: date)
    }

    private func getRecordDates() -> Set<Date> {
        return recordDates
    }

    private func getRecordsForSelectedDate() -> [RecordEntity] {
        return todayRecords
    }

    private func updateRecord(_ record: RecordEntity, with newText: String) {
        record.text = newText
        Task {
            await dataViewModel.refreshData()
        }
    }

    private func deleteRecord(_ record: RecordEntity) {
        Task {
            await dataViewModel.deleteRecord(record)
        }
    }

    // 生成分析报告
    private func generateAnalysisReport() {
        Task {
            // 检查记录数量是否达到15条
            let totalRecords = dataViewModel.records.count
            if totalRecords < 15 {
                await MainActor.run {
                    reportErrorMessage = "记录的数量不足15条，不能调用AI，记录数量越多，分析结果越准确。关注身体变化，坚持每天记录吧！"
                    showingReportError = true
                }
                return
            }

            isGeneratingReport = true

            do {
                let reportContent = try await AnalysisReportService.shared.generateAnalysisReport(
                    dataViewModel: dataViewModel,
                    startDate: reportStartDate,
                    endDate: reportEndDate
                )

                // 格式化时间范围
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                formatter.locale = Locale(identifier: "zh_CN")
                let startString = formatter.string(from: reportStartDate)
                let endString = formatter.string(from: reportEndDate)

                await MainActor.run {
                    analysisReportContent = reportContent
                    analysisTimeRange = "\(startString) ~ \(endString)"
                    isGeneratingReport = false
                    showingAnalysisReport = true
                }

            } catch {
                await MainActor.run {
                    isGeneratingReport = false

                    // 检查是否是调用次数不足的错误
                    if let analysisError = error as? AnalysisError,
                       case .insufficientAPICalls = analysisError {
                        showingInsufficientCallsAlert = true
                    } else {
                        reportErrorMessage = error.localizedDescription
                        showingReportError = true
                    }
                }
            }
        }
    }

    // 生成身体档案
    private func generateBodyArchive() {
        Task {
            // 检查记录数量是否达到15条
            let totalRecords = dataViewModel.records.count
            if totalRecords < 15 {
                await MainActor.run {
                    reportErrorMessage = "记录的数量不足15条，不能调用AI，记录数量越多，分析结果越准确。关注身体变化，坚持每天记录吧！"
                    showingReportError = true
                }
                return
            }

            isGeneratingArchive = true

            do {
                let archiveContent = try await BodyArchiveService.shared.generateBodyArchive(
                    dataViewModel: dataViewModel,
                    startDate: archiveStartDate,
                    endDate: archiveEndDate
                )

                // 格式化时间范围
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                formatter.locale = Locale(identifier: "zh_CN")
                let startString = formatter.string(from: archiveStartDate)
                let endString = formatter.string(from: archiveEndDate)

                await MainActor.run {
                    bodyArchiveContent = archiveContent
                    archiveTimeRange = "\(startString) ~ \(endString)"
                    isGeneratingArchive = false
                    showingBodyArchive = true
                }

            } catch {
                await MainActor.run {
                    isGeneratingArchive = false

                    // 检查是否是调用次数不足的错误
                    if let archiveError = error as? ArchiveError,
                       case .insufficientAPICalls = archiveError {
                        showingInsufficientCallsAlert = true
                    } else {
                        reportErrorMessage = error.localizedDescription
                        showingReportError = true
                    }
                }
            }
        }
    }

    // 月份切换函数
    private func changeMonth(by offset: Int) {
        guard !isAnimating else { return }

        // 设置动画方向
        animationDirection = offset > 0 ? .right : .left

        withAnimation(.easeInOut(duration: 0.3)) {
            isAnimating = true
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
            currentMonth = Calendar.current.date(byAdding: .month, value: offset, to: currentMonth) ?? currentMonth
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                isAnimating = false
                animationDirection = .none
            }
        }
    }
}

// 记录页面
struct RecordView: View {
    @ObservedObject var dataViewModel: DataViewModel
    @ObservedObject var keyboardManager: KeyboardManager
    @ObservedObject var speechRecognitionManager: SpeechRecognitionManager
    @State private var recordText: String = ""
    @State private var recordDays: Int = 0
    @State private var isTextEditorFocused: Bool = false
    @State private var showingDatePicker = false
    @State private var showingTimePicker = false
    @State private var selectedRecordDate = Date()
    @State private var selectedRecordTime = Date()
    @State private var showingToast = false
    @State private var toastMessage = ""
    @State private var toastType: ToastView.ToastType = .success

    var body: some View {
        VStack(spacing: 0) {
            // 主要内容区域
            VStack(spacing: 32) {
                Spacer()

                // 美化的输入框区域
                VStack(spacing: 20) {
                    // 透明背景的输入框设计
                    ZStack(alignment: .topLeading) {
                        // 输入框背景 - 保持透明
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.clear)
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                Color(red: 125/255, green: 175/255, blue: 106/255).opacity(isTextEditorFocused ? 0.6 : 0.2),
                                                Color(red: 145/255, green: 195/255, blue: 126/255).opacity(isTextEditorFocused ? 0.4 : 0.1)
                                            ]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        lineWidth: isTextEditorFocused ? 2 : 1
                                    )
                            )
                            .shadow(
                                color: Color(red: 125/255, green: 175/255, blue: 106/255).opacity(isTextEditorFocused ? 0.15 : 0.05),
                                radius: isTextEditorFocused ? 12 : 6,
                                x: 0,
                                y: isTextEditorFocused ? 6 : 3
                            )
                            .frame(minHeight: 220)
                            .animation(.easeInOut(duration: 0.3), value: isTextEditorFocused)

                        // 文本编辑器
                        TextEditor(text: $recordText)
                            .font(.custom("PingFang SC", size: 17))
                            .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))
                            .background(Color.clear)
                            .scrollContentBackground(.hidden)
                            .padding(20)
                            .onTapGesture {
                                isTextEditorFocused = true
                            }

                        // 优化的占位符文本
                        if recordText.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("记录你的身体感受...")
                                    .font(.custom("PingFang SC", size: 17))
                                    .fontWeight(.medium)
                                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.7))

                                Text("例如：头痛、胃部不适、疲劳等")
                                    .font(.custom("PingFang SC", size: 14))
                                    .foregroundColor(Color(red: 102/255, green: 102/255, blue: 102/255).opacity(0.6))

                                // 语音识别状态提示
                                if !speechRecognitionManager.permissionStatusMessage.isEmpty {
                                    Text(speechRecognitionManager.permissionStatusMessage)
                                        .font(.custom("PingFang SC", size: 12))
                                        .foregroundColor(.orange)
                                        .padding(.top, 4)
                                }

                                if speechRecognitionManager.canStartRecording {
                                    Text("按住下方绿色按钮开始语音记录")
                                        .font(.custom("PingFang SC", size: 12))
                                        .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                                        .padding(.top, 4)
                                }
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 28)
                            .allowsHitTesting(false)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.horizontal, 24)

                    // 美化的按钮区域
                    HStack(spacing: 16) {
                        // 记录过去按钮 - 次要按钮样式
                        EnhancedRecordButton(
                            title: "记录过去",
                            style: .secondary,
                            action: {
                                // 检查是否有输入内容
                                let text = recordText.trimmingCharacters(in: .whitespacesAndNewlines)
                                guard !text.isEmpty else { return }

                                // 显示日期选择弹窗
                                selectedRecordDate = Date()
                                showingDatePicker = true
                            }
                        )

                        // 记录现在按钮 - 主要按钮样式
                        EnhancedRecordButton(
                            title: "记录现在",
                            style: .primary,
                            action: {
                                saveRecord()
                            }
                        )
                    }
                    .padding(.horizontal, 24)
                }

                Spacer()

                // 美化的坚持天数显示
                VStack(spacing: 12) {
                    HStack(spacing: 8) {
                        // 小图标
                        Image(systemName: "calendar.badge.checkmark")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))

                        Text("你已经坚持记录了")
                            .font(.custom("PingFang SC", size: 16))
                            .foregroundColor(Color(red: 85/255, green: 85/255, blue: 85/255))

                        Text("\(recordDays)")
                            .font(.custom("PingFang SC", size: 20))
                            .fontWeight(.bold)
                            .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))

                        Text("天")
                            .font(.custom("PingFang SC", size: 16))
                            .foregroundColor(Color(red: 85/255, green: 85/255, blue: 85/255))
                    }

                    // 进度条或装饰元素
                    RoundedRectangle(cornerRadius: 2)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.3),
                                    Color(red: 145/255, green: 195/255, blue: 126/255).opacity(0.3)
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(width: 120, height: 4)
                }
                .padding(.bottom, 50)
            }
        }
        .onAppear {
            // 这里可以从数据库或UserDefaults加载实际的坚持天数
            loadRecordDays()

            // 请求语音识别权限
            Task {
                await speechRecognitionManager.requestPermissions()
            }
        }
        .onTapGesture {
            // 点击空白区域取消焦点
            isTextEditorFocused = false
            keyboardManager.dismissKeyboard()
        }
        .onChange(of: recordText) { _ in
            // 当文本改变时，设置焦点状态
            if !recordText.isEmpty && !isTextEditorFocused {
                isTextEditorFocused = true
            }
        }
        .onChange(of: speechRecognitionManager.recognizedText) { recognizedText in
            // 当语音识别文本变化时，更新输入框内容
            if !recognizedText.isEmpty {
                recordText = recognizedText
                isTextEditorFocused = true
            }
        }
        // 日期和时间选择弹窗覆盖层
        .overlay(
            ZStack {
                if showingDatePicker || showingTimePicker {
                    // 背景遮罩层
                    Color.black.opacity(0.4)
                        .ignoresSafeArea(.all)
                        .onTapGesture {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showingDatePicker = false
                                showingTimePicker = false
                            }
                        }
                        .transition(.opacity)

                    // 日期选择弹窗
                    if showingDatePicker {
                        VStack {
                            Spacer()
                            RecordDatePickerPopup(
                                isPresented: $showingDatePicker,
                                selectedDate: $selectedRecordDate,
                                onConfirm: { date in
                                    selectedRecordTime = date
                                    showingTimePicker = true
                                }
                            )
                            Spacer()
                        }
                        .transition(.asymmetric(
                            insertion: .scale(scale: 0.8).combined(with: .opacity),
                            removal: .scale(scale: 0.8).combined(with: .opacity)
                        ))
                    }

                    // 时间选择弹窗
                    if showingTimePicker {
                        VStack {
                            Spacer()
                            TimePickerPopup(
                                isPresented: $showingTimePicker,
                                selectedTime: $selectedRecordTime,
                                onConfirm: { finalDateTime in
                                    saveRecordToDateTime(finalDateTime)
                                }
                            )
                            Spacer()
                        }
                        .transition(.asymmetric(
                            insertion: .scale(scale: 0.8).combined(with: .opacity),
                            removal: .scale(scale: 0.8).combined(with: .opacity)
                        ))
                    }
                }
            }
            .animation(.easeInOut(duration: 0.3), value: showingDatePicker || showingTimePicker)
        )
        // 语音识别错误提示
        .alert("语音识别错误", isPresented: .constant(speechRecognitionManager.errorMessage != nil)) {
            Button("确定") {
                speechRecognitionManager.clearError()
            }
        } message: {
            Text(speechRecognitionManager.errorMessage ?? "")
        }
        // Toast 提示覆盖层
        .overlay(
            ZStack {
                if showingToast {
                    ToastView(
                        message: toastMessage,
                        type: toastType,
                        isShowing: $showingToast
                    )
                }
            }
            .animation(.easeInOut(duration: 0.3), value: showingToast)
        )
    }

    private func loadRecordDays() {
        // 从持久化存储加载坚持记录的天数（不重复日期数量）
        recordDays = dataViewModel.getRecordDaysCount()
    }

    private func saveRecord() {
        let text = recordText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !text.isEmpty else { return }

        Task {
            do {
                try await dataViewModel.addRecord(text: text, timestamp: Date())

                // 成功后的操作
                await MainActor.run {
                    recordText = "" // 清空输入框
                    isTextEditorFocused = false
                    loadRecordDays()

                    // 显示成功Toast
                    showToast(message: "记录成功！", type: .success)
                }
            } catch {
                // 显示失败Toast
                await MainActor.run {
                    showToast(message: "记录失败，请重试", type: .error)
                }
            }
        }
    }

    private func saveRecordToDate(_ date: Date) {
        let text = recordText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !text.isEmpty else { return }

        Task {
            try await dataViewModel.addRecord(text: text, timestamp: date)
            recordText = "" // 清空输入框
            isTextEditorFocused = false

            // 更新坚持天数
            loadRecordDays()
        }
    }

    private func saveRecordToDateTime(_ dateTime: Date) {
        let text = recordText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !text.isEmpty else { return }

        Task {
            do {
                try await dataViewModel.addRecord(text: text, timestamp: dateTime)

                // 成功后的操作
                await MainActor.run {
                    recordText = "" // 清空输入框
                    isTextEditorFocused = false
                    loadRecordDays()

                    // 显示成功Toast
                    let formatter = DateFormatter()
                    formatter.locale = Locale(identifier: "zh_CN")
                    formatter.dateFormat = "yyyy年M月d日 HH:mm"
                    showToast(message: "已成功记录到 \(formatter.string(from: dateTime))", type: .success)
                }
            } catch {
                // 显示失败Toast
                await MainActor.run {
                    showToast(message: "记录失败，请重试", type: .error)
                }
            }
        }
    }

    private func showToast(message: String, type: ToastView.ToastType) {
        toastMessage = message
        toastType = type
        withAnimation(.easeInOut(duration: 0.3)) {
            showingToast = true
        }
    }
}

// 个人中心页面
struct ProfileView: View {
    @ObservedObject var dataViewModel: DataViewModel
    @ObservedObject var keyboardManager: KeyboardManager
    @State private var selectedGender = "男"
    @State private var age = 30
    @State private var showGenderPicker = false
    @State private var showDatePicker = false
    @State private var showPurchasePage = false
    @State private var showHistoryGeneratedRecords = false
    @State private var showReminderSettings = false
    @State private var birthDate = Date()
    @State private var showClearDataConfirmation = false
    @State private var isClearingData = false
    @State private var clearDataError: String?
    @State private var showingToast = false
    @State private var toastMessage = ""
    @State private var toastType: ToastView.ToastType = .success
    @State private var showUserAgreement = false
    @State private var showPrivacyPolicy = false
    @State private var showProductIntroduction = false

    var body: some View {
        ZStack {
            // 背景渐变
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 226/255, green: 255/255, blue: 232/255),
                    Color.white
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
            .ignoresSafeArea()

            ScrollView {
                VStack(spacing: 24) {
                    // 顶部标题
                    HStack {
                        Text("基本信息，帮助分析更准确")
                            .font(.custom("PingFang SC", size: 16))
                            .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                        Spacer()
                    }
                    .padding(.top, 20)

                    // 基本信息部分 - 容器
                    VStack(spacing: 0) {
                        ProfileInfoRow(
                            icon: "性别",
                            title: "性别",
                            value: dataViewModel.userInfo?.userGender?.displayName ?? "未设置",
                            showArrow: true,
                            isFirst: true,
                            isLast: false
                        ) {
                            showGenderPicker = true
                        }

                        Divider()
                            .padding(.leading, 52)

                        ProfileInfoRow(
                            icon: "年龄",
                            title: "年龄",
                            value: "\(dataViewModel.userInfo?.age ?? 0)",
                            showArrow: true,
                            isFirst: false,
                            isLast: true
                        ) {
                            showDatePicker = true
                        }
                    }
                    .background(Color.white)
                    .cornerRadius(10)

                    // 调用AI次数部分 - 容器
                    VStack(spacing: 0) {
                        HStack {
                            Text("调用AI次数")
                                .font(.custom("PingFang SC", size: 16))
                                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                            Spacer()
                        }
                        .padding(.bottom, 16)

                        VStack(spacing: 0) {
                            ProfileInfoRow(
                                icon: "剩余",
                                title: "剩余次数",
                                value: "\(dataViewModel.userInfo?.remainingCalls ?? 0)",
                                showArrow: false,
                                isFirst: true,
                                isLast: false
                            ) {
                                // 剩余次数不可点击
                            }

                            Divider()
                                .padding(.leading, 52)

                            ProfileInfoRow(
                                icon: "购物车",
                                title: "购买次数",
                                value: "",
                                showArrow: true,
                                isFirst: false,
                                isLast: true
                            ) {
                                showPurchasePage = true
                            }
                        }
                        .background(Color.white)
                        .cornerRadius(10)
                    }

                    // 设置部分 - 容器
                    VStack(spacing: 0) {
                        HStack {
                            Text("设置")
                                .font(.custom("PingFang SC", size: 16))
                                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                            Spacer()
                        }
                        .padding(.bottom, 16)

                        VStack(spacing: 0) {
                            ProfileSettingRow(icon: "历史生成记录", title: "历史生成记录", isFirst: true, isLast: false) {
                                showHistoryGeneratedRecords = true
                            }

                            Divider().padding(.leading, 52)

                            ProfileSettingRow(icon: "提醒", title: "提醒设置", isFirst: false, isLast: false) {
                                showReminderSettings = true
                            }

                            Divider().padding(.leading, 52)

                            ProfileSettingRow(icon: "产品介绍", title: "产品介绍", isFirst: false, isLast: false) {
                                showProductIntroduction = true
                            }

                            Divider().padding(.leading, 52)

                            ProfileSettingRow(icon: "用户协议", title: "用户协议", isFirst: false, isLast: false) {
                                showUserAgreement = true
                            }

                            Divider().padding(.leading, 52)

                            ProfileSettingRow(icon: "隐私协议", title: "隐私政策", isFirst: false, isLast: false) {
                                showPrivacyPolicy = true
                            }

                            Divider().padding(.leading, 52)

                            HStack {
                                ProfileSettingRow(icon: "删除账号", title: "清除所有数据", isFirst: false, isLast: true) {
                                    if !isClearingData {
                                        showClearDataConfirmation = true
                                    }
                                }

                                if isClearingData {
                                    Spacer()
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .padding(.trailing, 16)
                                }
                            }
                        }
                        .background(Color.white)
                        .cornerRadius(10)
                    }

                    Spacer(minLength: 100) // 底部留白，避免被TabBar遮挡
                }
                .padding(.horizontal, 20)
            }
        }
        .sheet(isPresented: $showGenderPicker) {
            GenderPickerView(selectedGender: $selectedGender)
        }
        .onChange(of: showGenderPicker) { isShowing in
            // 当性别选择器关闭时，保存新的性别到数据库
            if !isShowing {
                Task {
                    // 将字符串转换为Gender枚举
                    let gender: UserInfoEntity.Gender
                    switch selectedGender {
                    case "男":
                        gender = .male
                    case "女":
                        gender = .female
                    default:
                        gender = .notSet
                    }
                    await dataViewModel.updateUserGender(gender)
                    await dataViewModel.refreshData()
                }
            }
        }
        .sheet(isPresented: $showDatePicker) {
            DatePickerView(birthDate: $birthDate, age: $age)
        }
        .onChange(of: showDatePicker) { isShowing in
            // 当日期选择器关闭时，保存新的出生日期到数据库
            if !isShowing {
                Task {
                    await dataViewModel.updateUserBirthDate(birthDate)
                    await dataViewModel.refreshData()
                }
            }
        }
        .sheet(isPresented: $showPurchasePage) {
            PurchaseCountView(dataViewModel: dataViewModel)
        }
        .sheet(isPresented: $showHistoryGeneratedRecords) {
            HistoryGeneratedRecordsView(dataViewModel: dataViewModel)
        }
        .sheet(isPresented: $showReminderSettings) {
            ReminderSettingsView(dataViewModel: dataViewModel)
        }
        .sheet(isPresented: $showUserAgreement) {
            UserAgreementView()
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            PrivacyPolicyView()
        }
        .sheet(isPresented: $showProductIntroduction) {
            ProductIntroductionView()
        }
        .alert("确认清除所有数据", isPresented: $showClearDataConfirmation) {
            Button("取消", role: .cancel) { }
            Button("确认清除", role: .destructive) {
                Task {
                    await clearAllData()
                }
            }
        } message: {
            Text("此操作将永久删除以下数据，且无法恢复：\n\n• 所有身体记录\n• 所有分析报告和身体档案\n• 个人信息设置\n• 所有提醒设置\n• 本地和iCloud同步数据\n\n请确认是否继续？")
        }
        .alert("清除数据失败", isPresented: .constant(clearDataError != nil)) {
            Button("确定") {
                clearDataError = nil
            }
        } message: {
            Text(clearDataError ?? "")
        }
        .onAppear {
            // 加载用户数据
            Task {
                await dataViewModel.refreshData()
            }
            if let userInfo = dataViewModel.userInfo {
                selectedGender = userInfo.userGender?.displayName ?? "未设置"
                birthDate = userInfo.birthDate ?? Date()
                calculateAge()
            }
        }
        .overlay(
            // Toast 提示
            ToastView(message: toastMessage, type: toastType, isShowing: $showingToast)
                .opacity(showingToast ? 1 : 0)
                .animation(.easeInOut(duration: 0.3), value: showingToast)
        )
    }

    private func calculateAge() {
        let calendar = Calendar.current
        let now = Date()
        let ageComponents = calendar.dateComponents([.year], from: birthDate, to: now)
        age = ageComponents.year ?? 30
    }

    // MARK: - 清除数据方法
    private func clearAllData() async {
        isClearingData = true
        clearDataError = nil

        do {
            try await dataViewModel.clearAllData()

            // 清除成功后刷新数据
            await dataViewModel.refreshData()

            // 显示成功提示
            DispatchQueue.main.async {
                self.isClearingData = false
                // 这里可以添加成功提示的Toast
                self.showToast(message: "所有数据已成功清除", type: .success)
            }
        } catch {
            DispatchQueue.main.async {
                self.isClearingData = false
                self.clearDataError = "清除数据失败：\(error.localizedDescription)"
            }
        }
    }

    // MARK: - Toast 方法
    private func showToast(message: String, type: ToastView.ToastType) {
        toastMessage = message
        toastType = type
        withAnimation(.easeInOut(duration: 0.3)) {
            showingToast = true
        }

        // 3秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            withAnimation(.easeInOut(duration: 0.3)) {
                showingToast = false
            }
        }
    }
}

// 增强的自定义TabBar
struct CustomTabBar: View {
    @Binding var selectedTab: Int
    @ObservedObject var speechRecognitionManager: SpeechRecognitionManager

    var body: some View {
        HStack {
            Spacer()

            // 历史记录按钮
            EnhancedTabBarButton(
                isSelected: selectedTab == 0,
                normalImage: "历史记录",
                selectedImage: "历史记录1",
                isLarge: false
            ) {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                    selectedTab = 0
                }
            }

            Spacer()

            // 记录按钮（中间的大按钮）
            RecordingTabBarButton(
                isSelected: selectedTab == 1,
                speechRecognitionManager: speechRecognitionManager
            ) {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                    selectedTab = 1
                }
            }

            Spacer()

            // 个人中心按钮
            EnhancedTabBarButton(
                isSelected: selectedTab == 2,
                normalImage: "个人中心",
                selectedImage: "个人中心1",
                isLarge: false
            ) {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                    selectedTab = 2
                }
            }

            Spacer()
        }
        .frame(height: 85)
        .background(Color.clear)
    }
}

// 增强的TabBar按钮组件
struct EnhancedTabBarButton: View {
    let isSelected: Bool
    let normalImage: String
    let selectedImage: String
    let isLarge: Bool
    let action: () -> Void
    @State private var isPressed: Bool = false

    private var iconSize: CGFloat {
        isLarge ? (isSelected ? 52 : 38) : 38
    }

    private var maxCircleSize: CGFloat {
        isLarge ? 90 : 65
    }

    var body: some View {
        Button(action: action) {
            ZStack {
                // 背景圆形容器
                Circle()
                    .fill(backgroundGradient)
                    .frame(width: circleSize, height: circleSize)
                    .shadow(
                        color: shadowColor,
                        radius: shadowRadius,
                        x: 0,
                        y: shadowOffset
                    )
                    .offset(y: isLarge && isSelected ? -18 : 0)
                    .scaleEffect(isPressed ? 0.95 : 1.0)

                // 图标
                Image(isSelected ? selectedImage : normalImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: iconSize, height: iconSize)
                    .foregroundColor(iconColor)
                    .offset(y: isLarge && isSelected ? -18 : 0)
                    .scaleEffect(isPressed ? 0.95 : 1.0)
            }
            .frame(width: maxCircleSize, height: maxCircleSize)
            .animation(.spring(response: 0.4, dampingFraction: 0.7), value: isSelected)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }

    private var circleSize: CGFloat {
        if isSelected {
            return isLarge ? 85 : 60
        } else {
            return isLarge ? 70 : 50
        }
    }

    private var backgroundGradient: LinearGradient {
        if isSelected {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 125/255, green: 175/255, blue: 106/255),
                    Color(red: 105/255, green: 155/255, blue: 86/255)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            return LinearGradient(
                gradient: Gradient(colors: [Color.clear]),
                startPoint: .top,
                endPoint: .bottom
            )
        }
    }

    private var iconColor: Color {
        isSelected ? .white : Color(red: 102/255, green: 102/255, blue: 102/255)
    }

    private var shadowColor: Color {
        isSelected ? Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.4) : Color.clear
    }

    private var shadowRadius: CGFloat {
        isSelected ? 12 : 0
    }

    private var shadowOffset: CGFloat {
        isSelected ? 6 : 0
    }
}

// 录音TabBar按钮组件
struct RecordingTabBarButton: View {
    let isSelected: Bool
    @ObservedObject var speechRecognitionManager: SpeechRecognitionManager
    let action: () -> Void
    @State private var isPressed: Bool = false
    @State private var isRecording: Bool = false

    private var iconSize: CGFloat {
        isSelected ? 52 : 38
    }

    private var maxCircleSize: CGFloat {
        90
    }

    var body: some View {
        Button(action: action) {
            ZStack {
                // 背景圆形容器
                Circle()
                    .fill(backgroundGradient)
                    .frame(width: circleSize, height: circleSize)
                    .shadow(
                        color: shadowColor,
                        radius: shadowRadius,
                        x: 0,
                        y: shadowOffset
                    )
                    .offset(y: isSelected ? -18 : 0)
                    .scaleEffect(isPressed ? 0.95 : 1.0)

                // 图标
                Image(currentImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: iconSize, height: iconSize)
                    .foregroundColor(iconColor)
                    .offset(y: isSelected ? -18 : 0)
                    .scaleEffect(isPressed ? 0.95 : 1.0)
            }
            .frame(width: maxCircleSize, height: maxCircleSize)
            .animation(.spring(response: 0.4, dampingFraction: 0.7), value: isSelected)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
            .animation(.easeInOut(duration: 0.2), value: isRecording)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
            if pressing && isSelected {
                // 开始录音
                startRecording()
            } else if !pressing && isRecording {
                // 停止录音
                stopRecording()
            }
        }, perform: {})
        .onChange(of: speechRecognitionManager.isRecording) { recording in
            isRecording = recording
        }
    }

    private var currentImage: String {
        if isRecording {
            return "录音"
        } else if isSelected {
            return "录音"
        } else {
            return "加号"
        }
    }

    private var circleSize: CGFloat {
        if isRecording {
            return 90
        } else if isSelected {
            return 85
        } else {
            return 70
        }
    }

    private var backgroundGradient: LinearGradient {
        if isRecording {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color.red.opacity(0.8),
                    Color.red.opacity(0.6)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else if isSelected {
            return LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 125/255, green: 175/255, blue: 106/255),
                    Color(red: 105/255, green: 155/255, blue: 86/255)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        } else {
            return LinearGradient(
                gradient: Gradient(colors: [Color.clear]),
                startPoint: .top,
                endPoint: .bottom
            )
        }
    }

    private var iconColor: Color {
        if isRecording || isSelected {
            return .white
        } else {
            return Color(red: 102/255, green: 102/255, blue: 102/255)
        }
    }

    private var shadowColor: Color {
        if isRecording {
            return Color.red.opacity(0.4)
        } else if isSelected {
            return Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.4)
        } else {
            return Color.clear
        }
    }

    private var shadowRadius: CGFloat {
        (isRecording || isSelected) ? 12 : 0
    }

    private var shadowOffset: CGFloat {
        (isRecording || isSelected) ? 6 : 0
    }

    private func startRecording() {
        Task {
            do {
                try await speechRecognitionManager.startRecording()
            } catch {
                print("开始录音失败: \(error)")
            }
        }
    }

    private func stopRecording() {
        speechRecognitionManager.stopRecording()
    }
}

// 历史记录数据模型
struct HistoryRecord: Identifiable {
    let id: UUID
    var date: Date
    var content: String
    let time: String
}

// 增强的日历网格视图
struct EnhancedCalendarGridView: View {
    let currentMonth: Date
    @Binding var selectedDate: Date
    let recordDates: Set<Date>

    private let calendar = Calendar.current
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter
    }()

    var body: some View {
        VStack(spacing: 12) {
            // 星期标题 - 美化设计
            HStack(spacing: 0) {
                ForEach(["日", "一", "二", "三", "四", "五", "六"], id: \.self) { weekday in
                    Text(weekday)
                        .font(.custom("PingFang SC", size: 14))
                        .fontWeight(.medium)
                        .foregroundColor(Color(red: 102/255, green: 102/255, blue: 102/255))
                        .frame(maxWidth: .infinity)
                        .frame(height: 24)
                }
            }

            // 日期网格 - 修复布局
            LazyVGrid(
                columns: Array(repeating: GridItem(.flexible(), spacing: 0), count: 7),
                spacing: 8
            ) {
                ForEach(getDaysInMonth(), id: \.self) { date in
                    if let date = date {
                        EnhancedDayView(
                            date: date,
                            isSelected: calendar.isDate(date, inSameDayAs: selectedDate),
                            hasRecord: recordDates.contains(calendar.startOfDay(for: date)),
                            isCurrentMonth: calendar.isDate(date, equalTo: currentMonth, toGranularity: .month)
                        ) {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                selectedDate = date
                            }
                        }
                    } else {
                        // 空白日期占位符 - 确保网格对齐
                        Rectangle()
                            .fill(Color.clear)
                            .frame(
                                width: UIScreen.isCompactWidth ? 36 : 40,
                                height: UIScreen.isSmallDevice ? 44 : 48
                            )
                    }
                }
            }
        }
    }

    private func getDaysInMonth() -> [Date?] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentMonth) else {
            return []
        }

        let firstOfMonth = monthInterval.start
        let firstWeekday = calendar.component(.weekday, from: firstOfMonth)
        let numberOfDaysInMonth = calendar.range(of: .day, in: .month, for: currentMonth)?.count ?? 0

        var days: [Date?] = []

        // 添加前面的空白日期
        for _ in 1..<firstWeekday {
            days.append(nil)
        }

        // 添加当月的日期
        for day in 1...numberOfDaysInMonth {
            if let date = calendar.date(byAdding: .day, value: day - 1, to: firstOfMonth) {
                days.append(date)
            }
        }

        return days
    }
}

// 专用的日历手势容器组件
struct CalendarGestureContainer: View {
    let currentMonth: Date
    @Binding var selectedDate: Date
    let recordDates: Set<Date>
    let animationDirection: AnimationDirection
    let isAnimating: Bool
    let onMonthChange: (Int) -> Void
    let getDaysInMonth: () -> [Date?]
    let getCalendarOffset: () -> CGFloat
    let getCalendarOpacity: () -> Double
    let calendar: Calendar

    var body: some View {
        ZStack {
            // 透明背景层，确保整个日历区域都能响应手势
            Rectangle()
                .fill(Color.clear)
                .contentShape(Rectangle()) // 确保透明区域也能响应手势

            // 当前日历
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                ForEach(getDaysInMonth(), id: \.self) { date in
                    if let date = date {
                        DayView(
                            date: date,
                            isSelected: calendar.isDate(date, inSameDayAs: selectedDate),
                            hasRecord: recordDates.contains(calendar.startOfDay(for: date)),
                            isCurrentMonth: calendar.isDate(date, equalTo: currentMonth, toGranularity: .month)
                        ) {
                            selectedDate = date
                        }
                    } else {
                        Color.clear
                            .frame(height: 40)
                            .contentShape(Rectangle()) // 确保空白区域也能响应手势
                    }
                }
            }
            .offset(x: getCalendarOffset())
            .opacity(getCalendarOpacity())
        }
        .clipped() // 确保滑出的内容不可见
        .contentShape(Rectangle()) // 确保整个容器区域都能响应手势
        .highPriorityGesture(
            // 使用高优先级手势，确保日历手势优先于任何父视图手势
            DragGesture(minimumDistance: 10, coordinateSpace: .local)
                .onEnded { value in
                    handleCalendarSwipeGesture(value)
                }
        )
        .simultaneousGesture(
            // 同时添加一个阻止手势传播的手势
            DragGesture(minimumDistance: 0)
                .onChanged { _ in
                    // 阻止手势向父视图传播
                }
        )
    }

    // 处理日历滑动手势的专用函数
    private func handleCalendarSwipeGesture(_ value: DragGesture.Value) {
        // 防止在动画进行中触发新的手势
        guard !isAnimating else { return }

        // 设置最小滑动距离阈值
        let minimumSwipeDistance: CGFloat = 50

        // 检查是否主要是水平滑动（防止垂直滑动误触）
        let horizontalDistance = abs(value.translation.width)
        let verticalDistance = abs(value.translation.height)

        // 只有当水平滑动距离大于垂直滑动距离且超过最小阈值时才响应
        guard horizontalDistance > verticalDistance && horizontalDistance > minimumSwipeDistance else {
            return
        }

        // 根据滑动方向切换月份
        if value.translation.width > 0 {
            // 向右滑动 - 切换到上个月
            onMonthChange(-1)
        } else {
            // 向左滑动 - 切换到下个月
            onMonthChange(1)
        }
    }
}

// 原始日历网格视图（保留兼容性）
struct CalendarGridView: View {
    let currentMonth: Date
    @Binding var selectedDate: Date
    let recordDates: Set<Date>

    private let calendar = Calendar.current
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter
    }()

    var body: some View {
        VStack(spacing: 8) {
            // 星期标题
            HStack {
                ForEach(["日", "一", "二", "三", "四", "五", "六"], id: \.self) { weekday in
                    Text(weekday)
                        .font(.custom("PingFang SC", size: 14))
                        .foregroundColor(Color(red: 102/255, green: 102/255, blue: 102/255))
                        .frame(maxWidth: .infinity)
                }
            }

            // 日期网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                ForEach(getDaysInMonth(), id: \.self) { date in
                    if let date = date {
                        DayView(
                            date: date,
                            isSelected: calendar.isDate(date, inSameDayAs: selectedDate),
                            hasRecord: recordDates.contains(calendar.startOfDay(for: date)),
                            isCurrentMonth: calendar.isDate(date, equalTo: currentMonth, toGranularity: .month)
                        ) {
                            selectedDate = date
                        }
                    } else {
                        Color.clear
                            .frame(height: 40)
                    }
                }
            }
        }
    }

    private func getDaysInMonth() -> [Date?] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentMonth) else {
            return Array(repeating: nil, count: 42) // 返回42个空位置
        }

        let firstOfMonth = monthInterval.start
        let firstWeekday = calendar.component(.weekday, from: firstOfMonth)
        let numberOfDaysInMonth = calendar.range(of: .day, in: .month, for: currentMonth)?.count ?? 0

        var days: [Date?] = []

        // 添加前面的空白天数
        for _ in 1..<firstWeekday {
            days.append(nil)
        }

        // 添加当月的天数
        for day in 1...numberOfDaysInMonth {
            if let date = calendar.date(byAdding: .day, value: day - 1, to: firstOfMonth) {
                days.append(date)
            }
        }

        // 填充到42个位置（6行 x 7列），确保日历高度固定
        while days.count < 42 {
            days.append(nil)
        }

        return days
    }
}

// 增强的单个日期视图
struct EnhancedDayView: View {
    let date: Date
    let isSelected: Bool
    let hasRecord: Bool
    let isCurrentMonth: Bool
    let action: () -> Void

    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter
    }()

    var body: some View {
        Button(action: action) {
            VStack(spacing: 2) {
                ZStack {
                    // 背景圆圈 - 响应式尺寸
                    Circle()
                        .fill(backgroundColor)
                        .frame(width: circleSize, height: circleSize)
                        .shadow(
                            color: isSelected ? Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.25) : Color.clear,
                            radius: isSelected ? 6 : 0,
                            x: 0,
                            y: isSelected ? 3 : 0
                        )

                    // 日期文字
                    Text(dateFormatter.string(from: date))
                        .font(.custom("PingFang SC", size: 15))
                        .fontWeight(isSelected ? .semibold : .medium)
                        .foregroundColor(textColor)
                }
                // 移除缩放效果避免布局变化
                .animation(.easeInOut(duration: 0.2), value: isSelected)

                // 记录指示点 - 固定位置
                Circle()
                    .fill(recordIndicatorColor)
                    .frame(width: hasRecord ? 4 : 4, height: hasRecord ? 4 : 4)
                    .opacity(hasRecord ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 0.2), value: hasRecord)
            }
            .frame(width: cellWidth, height: cellHeight) // 响应式整体尺寸
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 响应式尺寸计算
    private var circleSize: CGFloat {
        UIScreen.isCompactWidth ? 32 : 36
    }

    private var cellWidth: CGFloat {
        UIScreen.isCompactWidth ? 36 : 40
    }

    private var cellHeight: CGFloat {
        UIScreen.isSmallDevice ? 44 : 48
    }

    private var backgroundColor: Color {
        if isSelected {
            return Color(red: 125/255, green: 175/255, blue: 106/255)
        } else if hasRecord {
            return Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.1)
        }
        return Color.clear
    }

    private var textColor: Color {
        if isSelected {
            return .white
        } else if isCurrentMonth {
            return Color(red: 51/255, green: 51/255, blue: 51/255)
        } else {
            return Color(red: 153/255, green: 153/255, blue: 153/255)
        }
    }

    private var recordIndicatorColor: Color {
        if isSelected {
            return .white
        } else {
            return Color(red: 125/255, green: 175/255, blue: 106/255)
        }
    }
}

// 原始单个日期视图（保留兼容性）
struct DayView: View {
    let date: Date
    let isSelected: Bool
    let hasRecord: Bool
    let isCurrentMonth: Bool
    let action: () -> Void

    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter
    }()

    var body: some View {
        Button(action: action) {
            VStack(spacing: 2) {
                Text(dateFormatter.string(from: date))
                    .font(.custom("PingFang SC", size: 16))
                    .foregroundColor(textColor)
                    .frame(width: 32, height: 32)
                    .background(backgroundColor)
                    .clipShape(Circle())

                // 记录指示点
                Circle()
                    .fill(hasRecord ? Color(red: 51/255, green: 51/255, blue: 51/255) : Color.clear)
                    .frame(width: 4, height: 4)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var backgroundColor: Color {
        if isSelected {
            return Color(red: 125/255, green: 175/255, blue: 106/255)
        }
        return Color.clear
    }

    private var textColor: Color {
        if isSelected {
            return .white
        } else if isCurrentMonth {
            return Color(red: 51/255, green: 51/255, blue: 51/255)
        } else {
            return Color(red: 153/255, green: 153/255, blue: 153/255)
        }
    }
}

// 增强的历史记录行视图
struct EnhancedHistoryRecordRow: View {
    let record: RecordEntity
    let onTap: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // 左侧时间指示器
                VStack(spacing: 4) {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 125/255, green: 175/255, blue: 106/255),
                                    Color(red: 145/255, green: 195/255, blue: 126/255)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 12, height: 12)
                        .shadow(color: Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.3), radius: 2, x: 0, y: 1)

                    Text(record.formattedTime)
                        .font(.custom("PingFang SC", size: 11))
                        .fontWeight(.medium)
                        .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                }

                // 内容区域
                VStack(alignment: .leading, spacing: 6) {
                    Text(record.text ?? "")
                        .font(.custom("PingFang SC", size: 16))
                        .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    // 添加日期信息
                    Text(record.formattedDate)
                        .font(.custom("PingFang SC", size: 12))
                        .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))
                }

                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(Color(red: 187/255, green: 187/255, blue: 187/255))
            }
            .padding(.vertical, 16)
            .padding(.horizontal, 20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .shadow(color: Color.black.opacity(0.06), radius: 8, x: 0, y: 2)
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "M月d日"
        return formatter.string(from: date)
    }
}

// 原始历史记录行视图（保留兼容性）
struct HistoryRecordRow: View {
    let record: HistoryRecord
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 左侧圆圈
                Circle()
                    .fill(Color(red: 125/255, green: 175/255, blue: 106/255))
                    .frame(width: 8, height: 8)

                // 内容文本
                Text(record.content)
                    .font(.custom("PingFang SC", size: 16))
                    .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
                    .lineLimit(1)
                    .truncationMode(.tail)
                    .frame(maxWidth: .infinity, alignment: .leading)

                // 时间
                Text(record.time)
                    .font(.custom("PingFang SC", size: 12))
                    .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 16)
            .background(Color(red: 247/255, green: 250/255, blue: 247/255))
            .cornerRadius(6)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 记录详情弹窗
struct RecordDetailView: View {
    let record: HistoryRecord
    @Binding var editingText: String
    @Binding var isPresented: Bool
    let onSave: (String) -> Void
    let onDelete: () -> Void

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 内容编辑区域
                VStack(alignment: .leading, spacing: 8) {
                    TextEditor(text: $editingText)
                        .font(.custom("PingFang SC", size: 16))
                        .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
                        .padding(12)
                        .background(Color(red: 247/255, green: 250/255, blue: 247/255))
                        .cornerRadius(10)
                        .frame(minHeight: 120)
                }

                Spacer()

                // 底部按钮
                HStack(spacing: 16) {
                    Button(action: {
                        onDelete()
                        isPresented = false
                    }) {
                        Text("删除")
                            .font(.custom("PingFang SC", size: 16))
                            .fontWeight(.medium)
                            .foregroundColor(.red)
                            .frame(height: 40)
                            .frame(maxWidth: .infinity)
                            .background(Color.white)
                            .overlay(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color.red, lineWidth: 1)
                            )
                            .cornerRadius(10)
                    }

                    Button(action: {
                        onSave(editingText)
                        isPresented = false
                    }) {
                        Text("保存")
                            .font(.custom("PingFang SC", size: 16))
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .frame(height: 40)
                            .frame(maxWidth: .infinity)
                            .background(Color(red: 125/255, green: 175/255, blue: 106/255))
                            .cornerRadius(10)
                    }
                }
                .padding(.bottom, 24)
            }
            .padding(24)
            .navigationTitle("编辑记录")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("关闭") {
                    isPresented = false
                }
            )
        }
    }
}


// 增强的自定义记录详情视图
struct CustomRecordDetailView: View {
    let record: RecordEntity
    @Binding var editingText: String
    @Binding var isPresented: Bool
    let onSave: (String) -> Void
    let onDelete: () -> Void
    @ObservedObject var keyboardManager: KeyboardManager

    var body: some View {
        VStack(spacing: 0) {
            // 顶部拖拽指示器
            RoundedRectangle(cornerRadius: 3)
                .fill(Color(red: 187/255, green: 187/255, blue: 187/255))
                .frame(width: 36, height: 6)
                .padding(.top, 12)
                .padding(.bottom, 20)

            VStack(spacing: 20) {
                // 顶部标题栏
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("编辑记录")
                            .font(.custom("PingFang SC", size: 20))
                            .fontWeight(.bold)
                            .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))

                        Text(record.formattedDate + " " + record.formattedTime)
                            .font(.custom("PingFang SC", size: 14))
                            .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                    }

                    Spacer()

                    Button(action: {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(Color(red: 102/255, green: 102/255, blue: 102/255))
                            .frame(width: 36, height: 36)
                            .background(
                                Circle()
                                    .fill(.ultraThinMaterial)
                                    .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                            )
                    }
                }

                // 内容编辑区域
                VStack(alignment: .leading, spacing: 12) {
                    Text("记录内容")
                        .font(.custom("PingFang SC", size: 16))
                        .fontWeight(.semibold)
                        .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))

                    TextEditor(text: $editingText)
                        .font(.custom("PingFang SC", size: 16))
                        .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
                        .padding(16)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(.ultraThinMaterial)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.2), lineWidth: 1)
                                )
                        )
                        .frame(height: 140)
                        .scrollContentBackground(.hidden)
                }

                // 底部按钮
                HStack(spacing: 16) {
                    EnhancedActionButton(
                        title: "删除",
                        icon: "trash",
                        style: .outline
                    ) {
                        onDelete()
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .foregroundColor(.red)

                    EnhancedActionButton(
                        title: "保存",
                        icon: "checkmark",
                        style: .filled
                    ) {
                        onSave(editingText)
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                }
            }
            .padding(.horizontal, 24)
            .padding(.bottom, max(24, keyboardManager.keyboardHeight > 0 ? 8 : 24))
        }
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(.regularMaterial)
                .shadow(color: Color.black.opacity(0.15), radius: 24, x: 0, y: 12)
        )
        .padding(.horizontal, 20)
        .frame(maxWidth: 420)
        .ignoresSafeArea(.keyboard, edges: .bottom)
    }

    private func formatDateTime(_ date: Date, _ time: String) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "M月d日"
        return "\(formatter.string(from: date)) \(time)"
    }
}

// 动画方向枚举
enum AnimationDirection {
    case none
    case left
    case right
}

// 带滑动动画的日历网格视图
struct AnimatedCalendarGridView: View {
    let currentMonth: Date
    @Binding var selectedDate: Date
    let recordDates: Set<Date>
    let animationDirection: AnimationDirection
    let isAnimating: Bool

    // 添加回调函数来处理月份切换
    let onMonthChange: (Int) -> Void

    private let calendar = Calendar.current
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter
    }()

    var body: some View {
        VStack(spacing: 8) {
            // 星期标题 - 保持不动
            HStack {
                ForEach(["日", "一", "二", "三", "四", "五", "六"], id: \.self) { weekday in
                    Text(weekday)
                        .font(.custom("PingFang SC", size: 14))
                        .foregroundColor(Color(red: 102/255, green: 102/255, blue: 102/255))
                        .frame(maxWidth: .infinity)
                }
            }

            // 日历网格容器 - 独立的手势处理区域
            CalendarGestureContainer(
                currentMonth: currentMonth,
                selectedDate: $selectedDate,
                recordDates: recordDates,
                animationDirection: animationDirection,
                isAnimating: isAnimating,
                onMonthChange: onMonthChange,
                getDaysInMonth: getDaysInMonth,
                getCalendarOffset: getCalendarOffset,
                getCalendarOpacity: getCalendarOpacity,
                calendar: calendar
            )
        }
    }

    private func getCalendarOffset() -> CGFloat {
        guard isAnimating else { return 0 }

        switch animationDirection {
        case .left:
            return UIScreen.main.bounds.width // 向右滑出
        case .right:
            return -UIScreen.main.bounds.width // 向左滑出
        case .none:
            return 0
        }
    }

    private func getCalendarOpacity() -> Double {
        return isAnimating ? 0 : 1
    }

    private func getDaysInMonth() -> [Date?] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: currentMonth) else {
            return Array(repeating: nil, count: 42) // 返回42个空位置
        }

        let firstOfMonth = monthInterval.start
        let firstWeekday = calendar.component(.weekday, from: firstOfMonth)
        let numberOfDaysInMonth = calendar.range(of: .day, in: .month, for: currentMonth)?.count ?? 0

        var days: [Date?] = []

        // 添加前面的空白天数
        for _ in 1..<firstWeekday {
            days.append(nil)
        }

        // 添加当月的天数
        for day in 1...numberOfDaysInMonth {
            if let date = calendar.date(byAdding: .day, value: day - 1, to: firstOfMonth) {
                days.append(date)
            }
        }

        // 填充到42个位置（6行 x 7列），确保日历高度固定
        while days.count < 42 {
            days.append(nil)
        }

        return days
    }
}

// 增强的操作按钮组件
struct EnhancedActionButton: View {
    let title: String
    let icon: String
    let style: ButtonStyle
    let action: () -> Void
    @State private var isPressed = false

    enum ButtonStyle {
        case filled
        case outline
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))

                Text(title)
                    .font(.custom("PingFang SC", size: 16))
                    .fontWeight(.semibold)
            }
            .foregroundColor(textColor)
            .frame(height: 48)
            .frame(maxWidth: .infinity)
            .background(backgroundView)
            .scaleEffect(isPressed ? 0.96 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }

    private var textColor: Color {
        switch style {
        case .filled:
            return .white
        case .outline:
            return Color(red: 125/255, green: 175/255, blue: 106/255)
        }
    }

    @ViewBuilder
    private var backgroundView: some View {
        switch style {
        case .filled:
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(red: 125/255, green: 175/255, blue: 106/255),
                            Color(red: 105/255, green: 155/255, blue: 86/255)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.3), radius: 8, x: 0, y: 4)
        case .outline:
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 125/255, green: 175/255, blue: 106/255),
                                    Color(red: 145/255, green: 195/255, blue: 126/255)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                )
                .shadow(color: Color.black.opacity(0.06), radius: 6, x: 0, y: 2)
        }
    }
}

// 增强的记录按钮组件
struct EnhancedRecordButton: View {
    let title: String
    let style: RecordButtonStyle
    let action: () -> Void
    @State private var isPressed: Bool = false

    enum RecordButtonStyle {
        case primary
        case secondary
    }

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.custom("PingFang SC", size: 17))
                .fontWeight(.semibold)
                .foregroundColor(textColor)
                .frame(height: 52)
                .frame(maxWidth: .infinity)
                .background(backgroundView)
                .scaleEffect(isPressed ? 0.96 : 1.0)
                .animation(.easeInOut(duration: 0.15), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }

    private var textColor: Color {
        switch style {
        case .primary:
            return .white
        case .secondary:
            return Color(red: 34/255, green: 34/255, blue: 34/255)
        }
    }

    @ViewBuilder
    private var backgroundView: some View {
        switch style {
        case .primary:
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(red: 125/255, green: 175/255, blue: 106/255),
                            Color(red: 105/255, green: 155/255, blue: 86/255)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(
                    color: Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.4),
                    radius: 12,
                    x: 0,
                    y: 6
                )
        case .secondary:
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.3),
                                    Color(red: 145/255, green: 195/255, blue: 126/255).opacity(0.2)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1.5
                        )
                )
                .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 3)
        }
    }
}

// 隐藏键盘的扩展
extension View {
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// 个人信息行组件
struct ProfileInfoRow: View {
    let icon: String
    let title: String
    let value: String
    let showArrow: Bool
    let isFirst: Bool
    let isLast: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 图标
                Image(icon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 24, height: 24)

                // 标题
                Text(title)
                    .font(.custom("PingFang SC", size: 16))
                    .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))

                Spacer()

                // 值
                if !value.isEmpty {
                    Text(value)
                        .font(.custom("PingFang SC", size: 16))
                        .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
                }

                // 箭头
                if showArrow {
                    Image("进入")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 16, height: 16)
                }
            }
            .padding(.vertical, 16)
            .padding(.horizontal, 16)
            .background(Color.clear)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 设置行组件
struct ProfileSettingRow: View {
    let icon: String
    let title: String
    let isFirst: Bool
    let isLast: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 图标
                Image(icon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 24, height: 24)

                // 标题
                Text(title)
                    .font(.custom("PingFang SC", size: 16))
                    .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))

                Spacer()

                // 箭头
                Image("进入")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 16, height: 16)
            }
            .padding(.vertical, 16)
            .padding(.horizontal, 16)
            .background(Color.clear)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 性别选择器
struct GenderPickerView: View {
    @Binding var selectedGender: String
    @Environment(\.presentationMode) var presentationMode

    let genders = ["男", "女"]

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                ForEach(genders, id: \.self) { gender in
                    Button(action: {
                        selectedGender = gender
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack {
                            Text(gender)
                                .font(.custom("PingFang SC", size: 18))
                                .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))

                            Spacer()

                            if selectedGender == gender {
                                Image(systemName: "checkmark")
                                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                            }
                        }
                        .padding(.vertical, 16)
                        .padding(.horizontal, 20)
                        .background(Color.white)
                        .cornerRadius(10)
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                Spacer()
            }
            .padding(20)
            .background(Color(red: 247/255, green: 250/255, blue: 247/255))
            .navigationTitle("选择性别")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("完成") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

// 中文日期选择器
struct DatePickerView: View {
    @Binding var birthDate: Date
    @Binding var age: Int
    @Environment(\.presentationMode) var presentationMode

    @State private var selectedYear: Int = 1990
    @State private var selectedMonth: Int = 1
    @State private var selectedDay: Int = 1

    private let years = Array(1950...2050)
    private let months = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]
    private let days = Array(1...31)

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                HStack(spacing: 0) {
                    // 年份选择器
                    Picker("年", selection: $selectedYear) {
                        ForEach(years, id: \.self) { year in
                            Text(String(format: "%d年", year))
                                .font(.custom("PingFang SC", size: 18))
                                .tag(year)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(maxWidth: .infinity)

                    // 月份选择器
                    Picker("月", selection: $selectedMonth) {
                        ForEach(1...12, id: \.self) { month in
                            Text(months[month - 1])
                                .font(.custom("PingFang SC", size: 18))
                                .tag(month)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(maxWidth: .infinity)

                    // 日期选择器
                    Picker("日", selection: $selectedDay) {
                        ForEach(1...daysInMonth(year: selectedYear, month: selectedMonth), id: \.self) { day in
                            Text("\(day)日")
                                .font(.custom("PingFang SC", size: 18))
                                .tag(day)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                    .frame(maxWidth: .infinity)
                }
                .frame(height: 200)

                Spacer()
            }
            .padding(20)
            .background(Color(red: 247/255, green: 250/255, blue: 247/255))
            .navigationTitle("选择出生日期")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: Button("完成") {
                    updateBirthDate()
                    calculateAge()
                    presentationMode.wrappedValue.dismiss()
                }
            )
            .onAppear {
                initializeFromBirthDate()
            }
            .onChange(of: selectedYear) { _ in
                adjustDayIfNeeded()
            }
            .onChange(of: selectedMonth) { _ in
                adjustDayIfNeeded()
            }
        }
    }
}

// 记录日期选择弹窗
struct RecordDatePickerPopup: View {
    @Binding var isPresented: Bool
    @Binding var selectedDate: Date
    let onConfirm: (Date) -> Void

    @State private var selectedYear: Int = 2025
    @State private var selectedMonth: Int = 1
    @State private var selectedDay: Int = 1

    private let currentYear = Calendar.current.component(.year, from: Date())
    private let currentMonth = Calendar.current.component(.month, from: Date())
    private let currentDay = Calendar.current.component(.day, from: Date())
    private var years: [Int] { Array(2020...currentYear) }
    private let months = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]

    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Button("取消") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isPresented = false
                    }
                }
                .font(.custom("PingFang SC", size: 16))
                .foregroundColor(Color(red: 102/255, green: 102/255, blue: 102/255))

                Spacer()

                Text("选择记录日期")
                    .font(.custom("PingFang SC", size: 18))
                    .fontWeight(.semibold)
                    .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))

                Spacer()

                Button("确认") {
                    let calendar = Calendar.current
                    var components = DateComponents()
                    components.year = selectedYear
                    components.month = selectedMonth
                    components.day = selectedDay
                    components.hour = 12 // 设置为中午，避免时区问题

                    if let date = calendar.date(from: components) {
                        selectedDate = date
                        onConfirm(date)
                    }

                    withAnimation(.easeInOut(duration: 0.3)) {
                        isPresented = false
                    }
                }
                .font(.custom("PingFang SC", size: 16))
                .fontWeight(.semibold)
                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(Color.white)

            Divider()
                .background(Color(red: 240/255, green: 240/255, blue: 240/255))

            // 日期选择器
            HStack(spacing: 0) {
                // 年份选择器
                Picker("年", selection: $selectedYear) {
                    ForEach(years, id: \.self) { year in
                        Text(String(format: "%d年", year))
                            .font(.custom("PingFang SC", size: 18))
                            .tag(year)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(maxWidth: .infinity)

                // 月份选择器
                Picker("月", selection: $selectedMonth) {
                    ForEach(getAvailableMonths(), id: \.self) { month in
                        Text(months[month - 1])
                            .font(.custom("PingFang SC", size: 18))
                            .tag(month)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(maxWidth: .infinity)

                // 日期选择器
                Picker("日", selection: $selectedDay) {
                    ForEach(getAvailableDays(), id: \.self) { day in
                        Text("\(day)日")
                            .font(.custom("PingFang SC", size: 18))
                            .tag(day)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(maxWidth: .infinity)
            }
            .frame(height: 220)
            .background(Color.white)
        }
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
        .padding(.horizontal, 20)
        .onAppear {
            initializeFromSelectedDate()
        }
        .onChange(of: selectedYear) { _ in
            adjustDayIfNeeded()
        }
        .onChange(of: selectedMonth) { _ in
            adjustDayIfNeeded()
        }
    }

    private func initializeFromSelectedDate() {
        let calendar = Calendar.current
        let today = Date()
        selectedYear = calendar.component(.year, from: today)
        selectedMonth = calendar.component(.month, from: today)
        selectedDay = calendar.component(.day, from: today)
    }

    private func adjustDayIfNeeded() {
        // 首先检查月份是否有效
        if selectedYear == currentYear && selectedMonth > currentMonth {
            selectedMonth = currentMonth
        }

        // 然后检查日期是否有效
        let availableDays = getAvailableDays()
        let maxAvailableDay = availableDays.max() ?? 1

        if selectedDay > maxAvailableDay {
            selectedDay = maxAvailableDay
        }
    }

    private func daysInMonth(year: Int, month: Int) -> Int {
        let calendar = Calendar.current
        let dateComponents = DateComponents(year: year, month: month)
        let date = calendar.date(from: dateComponents)!
        let range = calendar.range(of: .day, in: .month, for: date)!
        return range.count
    }

    private func getAvailableMonths() -> [Int] {
        if selectedYear == currentYear {
            return Array(1...currentMonth)
        } else {
            return Array(1...12)
        }
    }

    private func getAvailableDays() -> [Int] {
        let maxDaysInMonth = daysInMonth(year: selectedYear, month: selectedMonth)

        if selectedYear == currentYear && selectedMonth == currentMonth {
            return Array(1...currentDay)
        } else {
            return Array(1...maxDaysInMonth)
        }
    }
}

// 时间选择弹窗
struct TimePickerPopup: View {
    @Binding var isPresented: Bool
    @Binding var selectedTime: Date
    let onConfirm: (Date) -> Void

    @State private var selectedHour: Int = 12
    @State private var selectedMinute: Int = 0

    private let hours = Array(0...23)
    private let minutes = Array(0...59)

    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Button("取消") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isPresented = false
                    }
                }
                .font(.custom("PingFang SC", size: 16))
                .foregroundColor(Color(red: 102/255, green: 102/255, blue: 102/255))

                Spacer()

                Text("选择记录时间")
                    .font(.custom("PingFang SC", size: 18))
                    .fontWeight(.semibold)
                    .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))

                Spacer()

                Button("确认") {
                    let calendar = Calendar.current
                    var components = calendar.dateComponents([.year, .month, .day], from: selectedTime)
                    components.hour = selectedHour
                    components.minute = selectedMinute

                    if let finalDate = calendar.date(from: components) {
                        selectedTime = finalDate
                        onConfirm(finalDate)
                    }

                    withAnimation(.easeInOut(duration: 0.3)) {
                        isPresented = false
                    }
                }
                .font(.custom("PingFang SC", size: 16))
                .fontWeight(.semibold)
                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(Color.white)

            Divider()
                .background(Color(red: 240/255, green: 240/255, blue: 240/255))

            // 时间选择器
            HStack(spacing: 0) {
                // 小时选择器
                Picker("小时", selection: $selectedHour) {
                    ForEach(hours, id: \.self) { hour in
                        Text(String(format: "%02d时", hour))
                            .font(.custom("PingFang SC", size: 18))
                            .tag(hour)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(maxWidth: .infinity)

                // 分钟选择器
                Picker("分钟", selection: $selectedMinute) {
                    ForEach(minutes, id: \.self) { minute in
                        Text(String(format: "%02d分", minute))
                            .font(.custom("PingFang SC", size: 18))
                            .tag(minute)
                    }
                }
                .pickerStyle(WheelPickerStyle())
                .frame(maxWidth: .infinity)
            }
            .frame(height: 220)
            .background(Color.white)
        }
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
        .padding(.horizontal, 20)
        .onAppear {
            initializeFromSelectedTime()
        }
    }

    private func initializeFromSelectedTime() {
        let calendar = Calendar.current
        selectedHour = calendar.component(.hour, from: selectedTime)
        selectedMinute = calendar.component(.minute, from: selectedTime)
    }
}

// Toast 组件
struct ToastView: View {
    let message: String
    let type: ToastType
    @Binding var isShowing: Bool

    enum ToastType {
        case success
        case error

        var backgroundColor: Color {
            switch self {
            case .success:
                return Color(red: 125/255, green: 175/255, blue: 106/255)
            case .error:
                return Color(red: 220/255, green: 53/255, blue: 69/255)
            }
        }

        var iconName: String {
            switch self {
            case .success:
                return "checkmark.circle.fill"
            case .error:
                return "xmark.circle.fill"
            }
        }
    }

    var body: some View {
        VStack {
            Spacer()

            HStack(spacing: 12) {
                Image(systemName: type.iconName)
                    .foregroundColor(.white)
                    .font(.system(size: 16, weight: .semibold))

                Text(message)
                    .font(.custom("PingFang SC", size: 14))
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.leading)

                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(type.backgroundColor)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
            .padding(.horizontal, 20)
            .padding(.bottom, 100) // 距离底部一定距离
        }
        .transition(.asymmetric(
            insertion: .move(edge: .bottom).combined(with: .opacity),
            removal: .move(edge: .bottom).combined(with: .opacity)
        ))
        .onAppear {
            // 自动隐藏Toast
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isShowing = false
                }
            }
        }
    }
}

// DatePickerView 的扩展方法
extension DatePickerView {
    private func initializeFromBirthDate() {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month, .day], from: birthDate)
        selectedYear = components.year ?? 1990
        selectedMonth = components.month ?? 1
        selectedDay = components.day ?? 1
    }

    private func updateBirthDate() {
        let calendar = Calendar.current
        var components = DateComponents()
        components.year = selectedYear
        components.month = selectedMonth
        components.day = selectedDay
        birthDate = calendar.date(from: components) ?? birthDate
    }

    private func calculateAge() {
        let calendar = Calendar.current
        let now = Date()
        let ageComponents = calendar.dateComponents([.year], from: birthDate, to: now)
        age = ageComponents.year ?? 30
    }

    private func daysInMonth(year: Int, month: Int) -> Int {
        let calendar = Calendar.current
        let dateComponents = DateComponents(year: year, month: month)
        let date = calendar.date(from: dateComponents)!
        let range = calendar.range(of: .day, in: .month, for: date)!
        return range.count
    }

    private func adjustDayIfNeeded() {
        let maxDays = daysInMonth(year: selectedYear, month: selectedMonth)
        if selectedDay > maxDays {
            selectedDay = maxDays
        }
    }
}



#Preview {
    ContentViewPreview()
}
