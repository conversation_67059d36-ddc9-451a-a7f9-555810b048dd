# 身体档案功能使用说明

## 功能概述

身体日记App现已集成AI身体档案功能，用户可以选择时间范围，让AI根据记录生成专业的身体档案，可用于就医时向医生提供参考。

## 使用流程

### 1. 进入历史记录页面
- 在App底部导航栏点击"历史记录"
- 进入历史记录页面

### 2. 点击生成身体档案按钮
- 在历史记录页面底部，点击"生成身体档案"按钮
- 按钮位于页面右下角，为绿色填充样式

### 3. 选择时间范围
- 弹出时间范围选择器
- 可以手动选择开始和结束日期
- 也可以使用快捷选择：最近7天、最近30天、最近90天
- 时间范围限制：最长不超过1年

### 4. 确认生成
- 点击"确定"按钮开始生成身体档案
- 系统会显示加载指示器："正在生成身体档案..."

### 5. 查看身体档案
- 生成完成后自动打开身体档案页面
- 档案包含以下内容：
  - 概览总结
  - Markdown表格展示所有记录
  - 症状趋势总结
  - 饮食与不适关系总结
  - 作息与身体反馈总结
  - 图表描述（文字描述）

## 身体档案特色功能

### 1. 医疗用途设计
- 专门为就医场景设计
- 包含医疗用途提示："此档案可用于就医时向医生提供参考"
- 数据格式专业，便于医生理解

### 2. 数据分析维度
- **症状趋势分析**：统计各症状在时间上的频率变化
- **饮食关联分析**：分析饮食事件与不适事件的相关性
- **作息影响分析**：统计"熬夜"等关键词与症状频次的关系

### 3. 分享和导出功能
- **文本分享**：可以通过系统分享功能分享档案内容
- **PDF导出**：支持导出为PDF文件（功能预留，后续版本实现）

## 技术实现

### 1. AI分析引擎
- 使用DeepSeek R1模型进行数据分析
- 基于专业的身体档案prompt模板
- 支持中文医疗术语和表达

### 2. 数据处理
- 自动提取用户信息（性别、年龄）
- 格式化时间范围内的所有记录
- 智能分类和统计分析

### 3. 存储和同步
- 生成的身体档案自动保存到CoreData
- 支持iCloud同步，多设备访问
- 与分析报告分开存储，便于管理

## 调用次数说明

### 消费规则
- 每次生成身体档案消费1次API调用次数
- 生成前会检查剩余调用次数
- 调用次数不足时会提示用户购买

### 购买方式
- 在个人中心页面查看剩余次数
- 点击购买按钮进行内购
- 100次调用 / 50元

## 错误处理

### 常见错误及解决方案

1. **"用户信息缺失"**
   - 请在个人中心完善性别和年龄信息

2. **"API调用次数不足"**
   - 请在个人中心购买更多调用次数

3. **"选择的时间范围内没有找到记录"**
   - 请选择有记录的时间范围
   - 或先添加一些身体记录

4. **"网络错误"**
   - 检查网络连接
   - 稍后重试

5. **"身体档案生成失败"**
   - 检查网络连接
   - 确认API服务正常
   - 稍后重试

## 数据隐私

### 安全保障
- 所有数据本地存储，通过iCloud同步
- API调用使用加密传输
- 不会存储用户个人隐私信息

### 数据使用
- 仅用于生成身体档案分析
- 不会用于其他商业用途
- 用户可随时删除数据

## 后续版本计划

### 即将推出的功能
1. **PDF导出**：完整的PDF生成和导出功能
2. **图表可视化**：使用Swift Charts生成实际图表
3. **医疗建议**：基于数据的个性化健康建议
4. **科室推荐**：根据症状推荐合适的医院科室

### 长期规划
1. **HealthKit集成**：自动导入健康数据
2. **药物追踪**：记录用药情况和效果
3. **医生分享**：安全地与医生分享档案
4. **健康评分**：综合健康状况评分系统

## 技术支持

如果在使用过程中遇到问题，请通过以下方式联系我们：
- 在App内"个人中心" → "联系我们"
- 发送邮件至：<EMAIL>
- 微信客服：BodyLogSupport

---

**注意**：身体档案仅供参考，不能替代专业医疗诊断。如有严重健康问题，请及时就医。
