//
//  SpeechRecognitionManager.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import Foundation
import Speech
import AVFoundation
import SwiftUI

/// 语音识别管理器
@MainActor
class SpeechRecognitionManager: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var isRecording = false
    @Published var recognizedText = ""
    @Published var authorizationStatus: SFSpeechRecognizerAuthorizationStatus = .notDetermined
    @Published var microphonePermissionStatus: AVAudioSession.RecordPermission = .undetermined
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private var speechRecognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let audioEngine = AVAudioEngine()
    
    // MARK: - Initialization
    override init() {
        super.init()
        setupSpeechRecognizer()
        checkPermissions()
    }
    
    // MARK: - Setup
    private func setupSpeechRecognizer() {
        // 设置中文语音识别器
        speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))
        speechRecognizer?.delegate = self
    }
    
    // MARK: - Permission Management
    func checkPermissions() {
        // 检查语音识别权限
        authorizationStatus = SFSpeechRecognizer.authorizationStatus()
        
        // 检查麦克风权限
        microphonePermissionStatus = AVAudioSession.sharedInstance().recordPermission
    }
    
    func requestPermissions() async {
        // 请求语音识别权限
        await requestSpeechRecognitionPermission()
        
        // 请求麦克风权限
        await requestMicrophonePermission()
    }
    
    private func requestSpeechRecognitionPermission() async {
        await withCheckedContinuation { continuation in
            SFSpeechRecognizer.requestAuthorization { status in
                DispatchQueue.main.async {
                    self.authorizationStatus = status
                    continuation.resume()
                }
            }
        }
    }
    
    private func requestMicrophonePermission() async {
        await withCheckedContinuation { continuation in
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                DispatchQueue.main.async {
                    self.microphonePermissionStatus = granted ? .granted : .denied
                    continuation.resume()
                }
            }
        }
    }
    
    // MARK: - Recording Control
    func startRecording() async throws {
        // 检查权限
        guard authorizationStatus == .authorized else {
            throw SpeechRecognitionError.speechRecognitionNotAuthorized
        }
        
        guard microphonePermissionStatus == .granted else {
            throw SpeechRecognitionError.microphoneNotAuthorized
        }
        
        guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
            throw SpeechRecognitionError.speechRecognizerNotAvailable
        }
        
        // 停止之前的识别任务
        stopRecording()
        
        // 配置音频会话
        try configureAudioSession()
        
        // 创建识别请求
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw SpeechRecognitionError.unableToCreateRequest
        }
        
        recognitionRequest.shouldReportPartialResults = true
        
        // 获取音频输入节点
        let inputNode = audioEngine.inputNode
        
        // 创建识别任务
        recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            DispatchQueue.main.async {
                if let result = result {
                    self?.recognizedText = result.bestTranscription.formattedString

                    // 如果识别任务完成且有结果，不显示错误
                    if result.isFinal {
                        // 识别完成，正常结束
                        return
                    }
                }

                if let error = error {
                    // 检查错误类型，只有真正的错误才显示弹窗
                    let nsError = error as NSError

                    // 如果是正常的识别完成，不显示错误
                    if nsError.domain == "kAFAssistantErrorDomain" && nsError.code == 216 {
                        // 这是正常的识别完成信号，不是错误
                        return
                    }

                    // 如果是因为没有检测到语音而结束，也不显示错误（因为用户可能只是短暂按下）
                    if nsError.domain == "kAFAssistantErrorDomain" && nsError.code == 203 {
                        // No speech detected - 这通常发生在录音时间很短的情况下
                        return
                    }

                    // 其他真正的错误才显示弹窗
                    self?.handleRecognitionError(error)
                }
            }
        }
        
        // 配置音频格式
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }
        
        // 启动音频引擎
        audioEngine.prepare()
        try audioEngine.start()
        
        isRecording = true
        recognizedText = ""
        errorMessage = nil
    }
    
    func stopRecording() {
        // 停止音频引擎
        if audioEngine.isRunning {
            audioEngine.stop()
            audioEngine.inputNode.removeTap(onBus: 0)
        }

        // 结束识别请求（不是取消，而是正常结束）
        recognitionRequest?.endAudio()
        recognitionRequest = nil

        // 完成识别任务（不是取消）
        recognitionTask?.finish()
        recognitionTask = nil

        isRecording = false
    }
    
    // MARK: - Audio Session Configuration
    private func configureAudioSession() throws {
        let audioSession = AVAudioSession.sharedInstance()
        try audioSession.setCategory(.record, mode: .measurement, options: .duckOthers)
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
    }
    
    // MARK: - Error Handling
    private func handleRecognitionError(_ error: Error) {
        stopRecording()

        let nsError = error as NSError

        // 根据错误类型提供更友好的错误信息
        switch nsError.code {
        case 203: // No speech detected
            // 不显示错误，这是正常情况
            return
        case 216: // Recognition task finished
            // 不显示错误，这是正常完成
            return
        case 1700: // Audio session error
            errorMessage = "音频设备忙碌，请稍后再试"
        case 1101: // Network error
            errorMessage = "网络连接问题，请检查网络后重试"
        default:
            // 只有在真正的错误情况下才显示错误信息
            if nsError.localizedDescription.contains("No speech detected") {
                // 不显示"未检测到语音"的错误
                return
            }
            errorMessage = "语音识别遇到问题，请重试"
        }
    }
    
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - Utility Methods
    var canStartRecording: Bool {
        return authorizationStatus == .authorized && 
               microphonePermissionStatus == .granted && 
               speechRecognizer?.isAvailable == true &&
               !isRecording
    }
    
    var permissionStatusMessage: String {
        if authorizationStatus != .authorized {
            return "需要语音识别权限"
        }
        if microphonePermissionStatus != .granted {
            return "需要麦克风权限"
        }
        if speechRecognizer?.isAvailable != true {
            return "语音识别不可用"
        }
        return ""
    }
}

// MARK: - SFSpeechRecognizerDelegate
extension SpeechRecognitionManager: SFSpeechRecognizerDelegate {
    nonisolated func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        // 语音识别可用性变化时的处理
        Task { @MainActor in
            if !available && isRecording {
                stopRecording()
                errorMessage = "语音识别服务暂时不可用"
            }
        }
    }
}

// MARK: - Error Types
enum SpeechRecognitionError: LocalizedError {
    case speechRecognitionNotAuthorized
    case microphoneNotAuthorized
    case speechRecognizerNotAvailable
    case unableToCreateRequest
    
    var errorDescription: String? {
        switch self {
        case .speechRecognitionNotAuthorized:
            return "语音识别权限未授权"
        case .microphoneNotAuthorized:
            return "麦克风权限未授权"
        case .speechRecognizerNotAvailable:
            return "语音识别器不可用"
        case .unableToCreateRequest:
            return "无法创建识别请求"
        }
    }
}
