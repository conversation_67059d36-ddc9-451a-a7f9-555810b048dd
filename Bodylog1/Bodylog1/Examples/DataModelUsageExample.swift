//
//  DataModelUsageExample.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import SwiftUI
import CoreData

/// 数据模型使用示例
struct DataModelUsageExample: View {
    @StateObject private var dataViewModel = DataViewModel()
    @State private var showingAddRecord = false
    @State private var newRecordText = ""
    
    var body: some View {
        NavigationView {
            VStack {
                // 用户信息显示
                if let userInfo = dataViewModel.userInfo {
                    UserInfoCard(userInfo: userInfo) {
                        // 购买更多调用次数的逻辑
                        Task {
                            await dataViewModel.addApiCalls(100)
                        }
                    }
                }
                
                // 统计信息
                ExampleStatisticsCard(dataViewModel: dataViewModel)
                
                // 记录列表
                RecordsList(dataViewModel: dataViewModel)
                
                Spacer()
            }
            .navigationTitle("身体日记")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("添加记录") {
                        showingAddRecord = true
                    }
                }
            }
            .sheet(isPresented: $showingAddRecord) {
                AddRecordSheet(dataViewModel: dataViewModel)
            }
        }
    }
}

// MARK: - 用户信息卡片
// UserInfoCard 已在 SharedComponents.swift 中定义，这里不重复定义

// MARK: - 统计信息卡片
struct ExampleStatisticsCard: View {
    @ObservedObject var dataViewModel: DataViewModel

    var body: some View {
        let stats = dataViewModel.getStatistics()

        StatisticsCard(
            totalRecords: stats.totalRecords,
            totalReports: stats.totalReports,
            activeReminders: stats.activeReminders
        )
        .padding(.horizontal)
    }
}

// MARK: - 记录列表
struct RecordsList: View {
    @ObservedObject var dataViewModel: DataViewModel
    
    var body: some View {
        VStack(alignment: .leading) {
            Text("最近记录")
                .font(.headline)
                .padding(.horizontal)
            
            if dataViewModel.records.isEmpty {
                Text("暂无记录")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(Array(dataViewModel.records.prefix(5)), id: \.objectID) { record in
                        RecordRow(record: record, dataViewModel: dataViewModel)
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

struct RecordRow: View {
    let record: RecordEntity
    @ObservedObject var dataViewModel: DataViewModel
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(record.text ?? "")
                    .font(.body)
                    .lineLimit(2)
                
                Text(record.formattedDate + " " + record.formattedTime)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button("删除") {
                Task {
                    await dataViewModel.deleteRecord(record)
                }
            }
            .font(.caption)
            .foregroundColor(.red)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .shadow(radius: 1)
    }
}

// MARK: - 添加记录表单
struct AddRecordSheet: View {
    @ObservedObject var dataViewModel: DataViewModel
    @Environment(\.presentationMode) var presentationMode
    
    @State private var recordText = ""
    @State private var selectedDate = Date()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("记录内容")
                        .font(.headline)
                    
                    TextEditor(text: $recordText)
                        .frame(minHeight: 100)
                        .padding(8)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("记录时间")
                        .font(.headline)
                    
                    DatePicker("选择时间", selection: $selectedDate, displayedComponents: [.date, .hourAndMinute])
                        .datePickerStyle(WheelDatePickerStyle())
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("添加记录")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: Button("保存") {
                    Task {
                        try await dataViewModel.addRecord(text: recordText, timestamp: selectedDate)
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                .disabled(recordText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            )
        }
    }
}

// MARK: - 示例用法说明
/*
 使用示例：

 1. 在 App 文件中设置 DataViewModel：
 ```swift
 @main
 struct Bodylog1App: App {
     let persistenceController = PersistenceController.shared
     
     var body: some Scene {
         WindowGroup {
             ContentView()
                 .environment(\.managedObjectContext, persistenceController.container.viewContext)
                 .environmentObject(DataViewModel())
         }
     }
 }
 ```

 2. 在视图中使用：
 ```swift
 struct ContentView: View {
     @EnvironmentObject var dataViewModel: DataViewModel
     
     var body: some View {
         // 使用 dataViewModel 进行数据操作
     }
 }
 ```

 3. 常用操作示例：
 
 // 添加记录
 await dataViewModel.addRecord(text: "今天头痛", timestamp: Date())
 
 // 获取今天的记录
 let todayRecords = dataViewModel.getRecords(for: Date())
 
 // 添加提醒
 await dataViewModel.addReminder(time: reminderTime, message: "记录身体状态")
 
 // 消费 API 调用次数
 let success = await dataViewModel.consumeApiCall()
 
 // 更新用户信息
 await dataViewModel.updateUserGender(.male)
 await dataViewModel.updateUserBirthDate(birthDate)
 
 // 添加报告
 await dataViewModel.addReport(
     title: "本周分析报告",
     type: .analysis,
     content: "分析内容...",
     timeRange: "2025年7月14日-20日"
 )
 */
