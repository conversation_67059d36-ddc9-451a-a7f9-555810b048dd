//
//  DataModelTests.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import Foundation
import CoreData
import SwiftUI

/// 数据模型测试和验证
struct DataModelTests {
    
    /// 测试 CoreData 实体创建
    static func testEntityCreation() {
        let context = PersistenceController.shared.container.viewContext
        
        print("🧪 开始测试 CoreData 实体创建...")
        
        // 测试 RecordEntity
        testRecordEntity(context: context)
        
        // 测试 ReportEntity
        testReportEntity(context: context)
        
        // 测试 UserInfoEntity
        testUserInfoEntity(context: context)
        
        // 测试 ReminderEntity
        testReminderEntity(context: context)
        
        print("✅ 所有实体测试完成！")
    }
    
    private static func testRecordEntity(context: NSManagedObjectContext) {
        print("📝 测试 RecordEntity...")
        
        let record = RecordEntity.create(
            in: context,
            text: "测试记录：今天头有点痛",
            timestamp: Date()
        )
        
        assert(record.id != nil, "RecordEntity ID 应该自动生成")
        assert(record.text == "测试记录：今天头有点痛", "RecordEntity text 应该正确设置")
        assert(record.timestamp != nil, "RecordEntity timestamp 应该设置")
        assert(record.isValid, "RecordEntity 应该有效")
        
        print("✅ RecordEntity 测试通过")
        
        // 清理测试数据
        context.delete(record)
        try? context.save()
    }
    
    private static func testReportEntity(context: NSManagedObjectContext) {
        print("📊 测试 ReportEntity...")
        
        let report = ReportEntity.create(
            in: context,
            title: "测试分析报告",
            type: .analysis,
            content: "这是一个测试报告内容",
            timeRange: "2025年7月20日"
        )
        
        assert(report.id != nil, "ReportEntity ID 应该自动生成")
        assert(report.title == "测试分析报告", "ReportEntity title 应该正确设置")
        assert(report.type == "分析报告", "ReportEntity type 应该正确设置")
        assert(report.content == "这是一个测试报告内容", "ReportEntity content 应该正确设置")
        assert(report.timeRange == "2025年7月20日", "ReportEntity timeRange 应该正确设置")
        assert(report.createdAt != nil, "ReportEntity createdAt 应该自动设置")
        assert(report.isValid, "ReportEntity 应该有效")
        assert(report.reportType == .analysis, "ReportEntity reportType 应该正确")
        
        print("✅ ReportEntity 测试通过")
        
        // 清理测试数据
        context.delete(report)
        try? context.save()
    }
    
    private static func testUserInfoEntity(context: NSManagedObjectContext) {
        print("👤 测试 UserInfoEntity...")
        
        let userInfo = UserInfoEntity.getOrCreate(in: context)
        
        assert(userInfo.remainingCalls == 20, "UserInfoEntity remainingCalls 应该默认为 20")
        
        // 测试消费调用次数
        let success = userInfo.consumeCalls(5)
        assert(success, "应该能够消费调用次数")
        assert(userInfo.remainingCalls == 15, "消费后剩余次数应该正确")
        
        // 测试增加调用次数
        userInfo.addCalls(10)
        assert(userInfo.remainingCalls == 25, "增加后剩余次数应该正确")
        
        // 测试性别设置
        userInfo.gender = UserInfoEntity.Gender.male.rawValue
        assert(userInfo.userGender == .male, "性别应该正确设置")
        
        // 测试年龄计算
        let birthDate = Calendar.current.date(byAdding: .year, value: -25, to: Date())!
        userInfo.birthDate = birthDate
        assert(userInfo.age == 25, "年龄计算应该正确")
        
        print("✅ UserInfoEntity 测试通过")
        
        try? context.save()
    }
    
    private static func testReminderEntity(context: NSManagedObjectContext) {
        print("⏰ 测试 ReminderEntity...")
        
        let reminderTime = Calendar.current.date(bySettingHour: 9, minute: 0, second: 0, of: Date())!
        let reminder = ReminderEntity.create(
            in: context,
            time: reminderTime,
            message: "测试提醒：记录身体状态"
        )
        
        assert(reminder.id != nil, "ReminderEntity ID 应该自动生成")
        assert(reminder.time == reminderTime, "ReminderEntity time 应该正确设置")
        assert(reminder.message == "测试提醒：记录身体状态", "ReminderEntity message 应该正确设置")
        assert(reminder.isActive == true, "ReminderEntity isActive 应该默认为 true")
        assert(reminder.isValid, "ReminderEntity 应该有效")
        
        // 测试切换激活状态
        reminder.toggleActive()
        assert(reminder.isActive == false, "切换后应该为 false")
        
        reminder.toggleActive()
        assert(reminder.isActive == true, "再次切换后应该为 true")
        
        // 测试时间组件
        let timeComponents = reminder.timeComponents
        assert(timeComponents.hour == 9, "小时应该正确")
        assert(timeComponents.minute == 0, "分钟应该正确")
        
        print("✅ ReminderEntity 测试通过")
        
        // 清理测试数据
        context.delete(reminder)
        try? context.save()
    }
}

/// 测试视图 - 可以在开发时使用
struct DataModelTestView: View {
    @State private var testResults = "点击按钮开始测试"
    
    var body: some View {
        VStack(spacing: 20) {
            Text("CoreData 数据模型测试")
                .font(.title)
                .fontWeight(.bold)
            
            ScrollView {
                Text(testResults)
                    .font(.system(.body, design: .monospaced))
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
            }
            
            Button("运行测试") {
                runTests()
            }
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(10)
        }
        .padding()
    }
    
    private func runTests() {
        testResults = "🧪 开始运行测试...\n"

        DispatchQueue.global(qos: .background).async {
            // 运行测试
            DataModelTests.testEntityCreation()

            DispatchQueue.main.async {
                testResults += "\n✅ 所有测试完成！\n"
                testResults += "📊 测试结果：所有 CoreData 实体创建和操作正常\n"
                testResults += "🎉 数据模型已准备就绪，可以在应用中使用"
            }
        }
    }
}

// MARK: - 使用说明
/*
 在开发过程中，你可以这样使用测试：

 1. 在 ContentView 中临时添加测试按钮：
 ```swift
 Button("测试数据模型") {
     DataModelTests.testEntityCreation()
 }
 ```

 2. 或者创建一个专门的测试视图：
 ```swift
 NavigationLink("数据模型测试") {
     DataModelTestView()
 }
 ```

 3. 在实际使用前验证数据模型：
 ```swift
 // 在 App 启动时运行一次测试
 DataModelTests.testEntityCreation()
 ```
 */
