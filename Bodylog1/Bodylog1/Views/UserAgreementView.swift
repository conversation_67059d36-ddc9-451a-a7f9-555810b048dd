//
//  UserAgreementView.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/21.
//

import SwiftUI

/// 用户服务协议页面
struct UserAgreementView: View {
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景渐变
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 226/255, green: 255/255, blue: 232/255),
                        Color.white
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(alignment: .leading, spacing: 16) {
                        // 协议内容
                        Text(userAgreementContent)
                            .font(.system(size: 14, weight: .regular))
                            .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
                            .lineSpacing(4)
                            .textSelection(.enabled) // 支持文本选择和复制
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                    }
                }
            }
            .navigationTitle("用户服务协议")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("返回") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                }
            }
        }
    }
    
    /// 用户服务协议内容
    private var userAgreementContent: String {
        return """
身体日记 - 用户服务协议

生效日期：2025年7月21日  
更新日期：2025年7月21日

---

第一章 总则

1.1 协议说明

本《用户服务协议》（以下简称"本协议"）是您与身体日记应用开发者（以下简称"我们"或"开发者"）之间关于使用身体日记移动应用程序（以下简称"本应用"或"身体日记"）所订立的协议。

1.2 应用用途

身体日记是一款专注于个人健康记录的移动应用，旨在帮助用户：
- 记录日常身体状况（如头痛、胃痛、腹泻、饮食、作息等）
- 通过AI模型（DeepSeek）生成个人健康分析报告
- 生成专业的身体档案，辅助用户关注健康趋势

1.3 重要免责声明

本应用仅供健康记录和趋势分析参考，不提供医疗诊断服务。
- 本应用生成的所有分析报告和身体档案均基于AI算法，仅供参考
- 任何分析结果都不能替代专业医生的诊断和治疗建议
- 如发现身体异常或症状严重，请及时就医咨询专业医生
- 用户应理性对待AI分析结果，不应将其作为医疗决策的唯一依据

1.4 协议接受

通过下载、安装、注册或使用本应用，您表示已阅读、理解并同意接受本协议的全部条款。如您不同意本协议的任何条款，请立即停止使用本应用。

---

第二章 用户注册与使用

2.1 无需注册机制

本应用采用无需注册的使用模式：
- 用户无需创建专门的账户即可使用本应用
- 应用依托Apple ID和iCloud服务进行数据同步
- 用户的使用权限与Apple ID绑定

2.2 Apple ID要求

使用本应用需要：
- 拥有有效的Apple ID账户
- 在设备上登录Apple ID
- 建议开启iCloud服务以获得最佳体验

2.3 用户责任

用户需自行确保：
- Apple ID账户的安全性
- iCloud服务的正常使用
- 设备系统版本符合要求（iOS 16.6及以上）
- 网络连接的稳定性（使用AI功能时）

2.4 使用条件

用户使用本应用应当：
- 年满18周岁或在监护人同意下使用
- 具备完全民事行为能力
- 遵守当地法律法规
- 不得将应用用于违法用途

---

第三章 数据存储与隐私保护

3.1 数据存储方式

本应用采用以下数据存储方式：
- 本地存储：用户的健康记录首先存储在设备本地
- iCloud同步：通过Apple的iCloud服务实现多设备数据同步
- 无服务器存储：开发者不设立自建服务器，不收集用户数据

3.2 数据类型

本应用可能存储的数据包括：
- 用户输入的身体状况记录
- 用户设置的个人信息（性别、年龄）
- AI生成的分析报告和身体档案
- 应用使用偏好设置
- 提醒设置信息

3.3 隐私保护承诺

我们承诺：
- 不收集个人隐私数据：开发者无法访问用户的健康记录
- 不上传用户数据：用户数据不会上传至开发者服务器
- Apple负责同步：数据同步完全由Apple iCloud服务负责
- 本地优先原则：所有数据优先存储在用户设备本地

3.4 第三方服务

本应用使用以下第三方服务：
- DeepSeek AI服务：用于生成分析报告，仅在用户主动请求时调用
- Apple iCloud：用于数据同步，遵循Apple隐私政策
- Apple App Store：用于应用分发和内购服务
- RevenueCat服务：用于内购管理和跨设备购买同步

3.5 隐私政策遵循

本应用严格遵守《隐私政策》的相关规定，用户可在应用内查看完整的隐私政策内容。

---

第四章 AI分析与免责声明

4.1 AI功能说明

本应用提供的AI功能包括：
- 分析报告生成：基于用户记录生成健康趋势分析
- 身体档案生成：生成专业格式的身体状况档案
- 健康建议提供：基于数据分析提供一般性健康建议

4.2 AI技术说明

- 使用模型：DeepSeek R1大语言模型
- 分析基础：基于循证医学知识和用户输入数据
- 处理方式：采用专业的医疗术语和分析框架
- 语言支持：支持中文医疗术语和表达

4.3 AI分析免责声明

重要提醒：AI分析结果仅供参考，不能替代医生诊断

用户理解并同意：
- AI分析基于算法模型，可能存在误差或局限性
- 分析结果不构成医疗诊断或治疗建议
- 任何健康决策应咨询专业医疗人员
- 开发者不对AI分析结果的准确性承担责任
- 用户应理性对待分析结果，不应过度依赖

4.4 就医建议

当出现以下情况时，用户应立即就医：
- 身体出现严重不适症状
- 症状持续恶化或反复出现
- AI分析提示可能存在健康风险
- 任何需要专业医疗判断的情况

4.5 数据质量影响

AI分析质量受以下因素影响：
- 用户输入数据的准确性和完整性
- 记录的时间跨度和频率
- 症状描述的详细程度
- 用户个人信息的完整性

---

第五章 付费服务与调用次数

5.1 付费模式说明

本应用采用按次付费的商业模式：
- 免费功能：基础记录功能完全免费
- 付费功能：AI分析报告和身体档案生成需要消费调用次数
- 新用户福利：新用户免费获得10次AI调用机会

5.2 调用次数规则

- 消费规则：每次生成分析报告或身体档案消费1次调用次数
- 检查机制：生成前自动检查剩余调用次数
- 不足提醒：调用次数不足时会提示用户购买

5.3 购买方式

- 购买渠道：通过Apple App Store内购系统购买
- 技术实现：使用RevenueCat+StoreKit2技术方案
- 购买管理：购买记录由Apple App Store和RevenueCat共同管理
- 价格体系：具体价格以App Store显示为准

5.4 购买条款

- 即时生效：购买成功后调用次数立即到账
- 不可退款：已购买的调用次数不支持退款
- 不可转让：调用次数仅限购买账户使用
- 有效期限：调用次数无使用期限限制
- 跨设备同步：通过RevenueCat实现购买记录的跨设备同步

5.5 第三方服务管理

5.5.1 Apple App Store
- 支付处理：由Apple App Store完成
- 购买凭证：由Apple系统生成和验证
- 退款申请：需向Apple提出
- 购买争议：由Apple客服处理

5.5.2 RevenueCat服务
- 服务提供商：RevenueCat Inc.
- 服务内容：内购管理、收据验证、跨设备同步
- 数据处理：处理购买相关的匿名化数据
- 隐私保护：遵循RevenueCat的隐私政策

---

第六章 用户义务与行为规范

6.1 合法使用

用户不得将本应用用于：
- 传播虚假医疗信息
- 恶意测试或攻击系统
- 其他违反法律法规的行为

6.2 知识产权尊重

用户应当：
- 尊重应用的知识产权
- 不得逆向工程或破解应用
- 不得复制或盗用应用内容
- 不得进行商业性转售

6.3 系统安全

用户有义务：
- 保护自己的Apple ID安全
- 及时更新应用版本
- 报告发现的安全漏洞

6.4 禁止行为

严格禁止以下行为：
- 利用技术手段绕过付费机制
- 恶意消耗系统资源
- 传播有害信息
- 侵犯他人隐私权益
- 进行任何形式的网络攻击

---

第七章 数据管理与清除

.1 数据自主权

用户对自己的数据享有完全控制权：
- 可以随时查看存储的数据
- 可以编辑或删除任何记录
- 可以导出个人数据
- 可以完全清除所有数据

7.2 数据清除功能

本应用提供完整的数据清除功能：
- 清除范围：所有身体记录、个人信息、分析报告、提醒设置
- 清除方式：在个人中心手动执行清除操作
- 确认机制：需要用户明确确认才能执行
- 不可恢复：清除操作不可逆，无法恢复数据

7.3 iCloud数据清除

数据清除将同时影响：
- 设备本地存储的数据
- iCloud同步的数据
- 其他设备上的同步数据

7.4 卸载应用

当用户卸载应用时：
- 本地存储数据将被删除
- iCloud数据可能保留
- 建议卸载前手动清除数据

7.5 数据保护

用户应当：
- 定期备份重要数据
- 谨慎使用数据清除功能
- 了解数据清除的不可逆性
- 在清除前确认操作意图

---

第八章 知识产权

.1 应用知识产权

本应用的所有知识产权归开发者所有，包括但不限于：
- 应用程序代码和架构
- 用户界面设计
- 文档和说明材料
- 商标和品牌标识

8.2 用户数据权利

用户对自己生成的数据享有权利：
- 用户输入的身体记录归用户所有
- 用户设置的个人信息归用户所有
- 用户有权控制数据的使用和分享

8.3 AI生成内容

关于AI生成的分析报告和身体档案：
- 内容基于用户数据生成，归用户使用
- 用户可以自由分享和使用这些内容
- 开发者不主张对AI生成内容的版权

8.4 使用许可

开发者授予用户有限的、非独占的、不可转让的使用许可：
- 仅限个人非商业用途使用
- 不得进行逆向工程
- 不得复制或分发应用
- 许可随协议终止而终止

---

第九章 法律适用与争议解决

9.1 适用法律

本协议的签订、履行、解释及争议解决均适用中华人民共和国法律法规。

9.2 管辖法院

因本协议引起的或与本协议有关的任何争议，双方应首先通过友好协商解决。协商不成的，任何一方均可向开发者所在地有管辖权的人民法院提起诉讼。

9.3 争议解决程序

1. 协商解决：争议发生后，双方应首先通过友好协商解决
2. 调解程序：协商不成可申请第三方调解
3. 仲裁程序：双方可协商选择仲裁解决
4. 诉讼程序：最终可通过法院诉讼解决

### 9.4 法律合规

本应用严格遵守相关法律法规：
- 《网络安全法》
- 《个人信息保护法》
- 《数据安全法》
- 《消费者权益保护法》
- 其他相关法律法规

9.5 违约责任

- 用户违反本协议的，开发者有权终止服务
- 开发者违反本协议的，应承担相应法律责任

---

第十章 协议更新与其他条款

10.1 协议更新

开发者保留随时修改本协议的权利：
- 更新通知：重大更新将在应用内通知用户
- 生效时间：更新后的协议自发布之日起生效
- 用户选择：用户可选择接受新协议或停止使用应用
- 查看方式：用户可在应用内随时查看最新版本协议

10.2 协议终止

以下情况下本协议将终止：
- 用户主动卸载应用
- 用户违反协议条款被终止服务
- 应用停止运营
- 双方协商一致终止

10.3 协议效力

- 本协议部分条款无效不影响其他条款效力
- 协议终止后，相关权利义务条款继续有效
- 本协议与隐私政策等文件共同构成完整协议

10.4 联系方式

如有任何问题或建议，请通过以下方式联系我们：
- 邮件联系：<EMAIL>

10.5 其他条款

- 本协议的标题仅为方便阅读，不影响条款解释
- 本协议构成双方就相关事项的完整协议
- 任何口头承诺不能修改本协议内容
- 本协议的修改必须以书面形式进行

---

附则

协议解释权

本协议的最终解释权归身体日记应用开发者所有。

协议生效

本协议自用户首次使用本应用时生效，直至用户停止使用本应用或协议被终止。

特别提醒

请用户仔细阅读本协议，特别是免责声明、付费条款、数据处理等重要条款。如有疑问，请在使用前咨询相关专业人士。

---

身体日记开发团队  
2025年7月21日
"""
    }
}

#Preview {
    UserAgreementView()
}
