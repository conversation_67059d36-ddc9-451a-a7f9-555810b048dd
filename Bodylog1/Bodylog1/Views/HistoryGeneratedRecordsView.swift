//
//  HistoryGeneratedRecordsView.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/21.
//

import SwiftUI
import UniformTypeIdentifiers

/// 历史生成记录页面
struct HistoryGeneratedRecordsView: View {
    @ObservedObject var dataViewModel: DataViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var selectedSegment = 0
    @State private var showingReportDetail = false
    @State private var selectedReport: ReportEntity?
    @State private var isReportReady = false

    // 分段选项
    private let segments = ["分析报告", "身体档案"]

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 分段控制器
                segmentedControl

                // 内容区域
                contentArea
            }
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 226/255, green: 255/255, blue: 232/255),
                        Color.white
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationTitle("历史生成记录")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingReportDetail) {
            if let report = selectedReport, isReportReady {
                ReportDetailView(report: report)
                    .onAppear {
                        print("🔍 [HistoryGeneratedRecords] ReportDetailView已显示")
                        print("🔍 [HistoryGeneratedRecords] Sheet即将显示 - 报告标题: \(report.title ?? "无标题")")
                        print("🔍 [HistoryGeneratedRecords] Sheet即将显示 - 报告内容: \(String(report.content?.prefix(100) ?? "无内容"))")
                    }
            } else {
                VStack {
                    ProgressView("加载中...")
                        .padding()
                    Text("正在准备报告内容")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
                .onAppear {
                    print("❌ [HistoryGeneratedRecords] Sheet显示失败 - selectedReport: \(selectedReport != nil ? "存在" : "nil"), isReportReady: \(isReportReady)")
                    // 如果数据还没准备好，尝试重新触发
                    if selectedReport != nil && !isReportReady {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            isReportReady = true
                        }
                    }
                }
            }
        }
        .onAppear {
            print("🔍 [HistoryGeneratedRecords] 页面出现")
            Task {
                await dataViewModel.refreshData()
            }
        }
        .onChange(of: showingReportDetail) { isShowing in
            if !isShowing {
                // Sheet关闭时重置状态
                print("🔍 [HistoryGeneratedRecords] Sheet已关闭，重置状态")
                selectedReport = nil
                isReportReady = false
            }
        }
    }

    // MARK: - 分段控制器
    private var segmentedControl: some View {
        VStack(spacing: 0) {
            HStack(spacing: 0) {
                ForEach(0..<segments.count, id: \.self) { index in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            selectedSegment = index
                        }
                    }) {
                        Text(segments[index])
                            .font(.custom("PingFang SC", size: 16))
                            .fontWeight(.medium)
                            .foregroundColor(selectedSegment == index ?
                                           Color(red: 125/255, green: 175/255, blue: 106/255) :
                                           Color(red: 153/255, green: 153/255, blue: 153/255))
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                    }
                }
            }
            .background(Color.white)
            .cornerRadius(8)
            .overlay(
                // 下划线指示器
                HStack {
                    if selectedSegment == 0 {
                        Rectangle()
                            .fill(Color(red: 125/255, green: 175/255, blue: 106/255))
                            .frame(height: 2)
                            .frame(maxWidth: .infinity)
                        Spacer()
                    } else {
                        Spacer()
                        Rectangle()
                            .fill(Color(red: 125/255, green: 175/255, blue: 106/255))
                            .frame(height: 2)
                            .frame(maxWidth: .infinity)
                    }
                }
                .padding(.horizontal, 0),
                alignment: .bottom
            )
        }
        .padding(.horizontal, 20)
        .padding(.top, 16)
        .padding(.bottom, 8)
    }

    // MARK: - 内容区域
    private var contentArea: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                if selectedSegment == 0 {
                    // 分析报告列表
                    analysisReportsList
                } else {
                    // 身体档案列表
                    bodyArchivesList
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
        }
    }

    // MARK: - 分析报告列表
    private var analysisReportsList: some View {
        Group {
            let analysisReports = dataViewModel.getReports(ofType: .analysis)

            if analysisReports.isEmpty {
                emptyStateView(type: "分析报告")
            } else {
                ForEach(analysisReports.sorted(by: { $0.createdAt ?? Date() > $1.createdAt ?? Date() })) { report in
                    HistoryReportRowView(report: report) {
                        print("🔍 [HistoryGeneratedRecords] 用户点击分析报告 - 标题: \(report.title ?? "无标题"), ID: \(report.id?.uuidString ?? "无ID")")
                        print("🔍 [HistoryGeneratedRecords] 报告内容长度: \(report.content?.count ?? 0)")
                        print("🔍 [HistoryGeneratedRecords] 报告类型: \(report.type ?? "无类型")")

                        // 确保状态重置和正确设置
                        isReportReady = false
                        selectedReport = nil

                        // 使用异步方式确保状态更新完成
                        DispatchQueue.main.async {
                            selectedReport = report
                            isReportReady = true

                            // 延迟一个runloop确保状态完全更新
                            DispatchQueue.main.async {
                                showingReportDetail = true
                                print("🔍 [HistoryGeneratedRecords] 状态设置完成，显示详情页")
                            }
                        }
                    }
                }
            }
        }
    }

    // MARK: - 身体档案列表
    private var bodyArchivesList: some View {
        Group {
            let bodyArchives = dataViewModel.getReports(ofType: .archive)

            if bodyArchives.isEmpty {
                emptyStateView(type: "身体档案")
            } else {
                ForEach(bodyArchives.sorted(by: { $0.createdAt ?? Date() > $1.createdAt ?? Date() })) { report in
                    HistoryReportRowView(report: report) {
                        print("🔍 [HistoryGeneratedRecords] 用户点击身体档案 - 标题: \(report.title ?? "无标题"), ID: \(report.id?.uuidString ?? "无ID")")
                        print("🔍 [HistoryGeneratedRecords] 档案内容长度: \(report.content?.count ?? 0)")
                        print("🔍 [HistoryGeneratedRecords] 档案类型: \(report.type ?? "无类型")")

                        // 确保状态重置和正确设置
                        isReportReady = false
                        selectedReport = nil

                        // 使用异步方式确保状态更新完成
                        DispatchQueue.main.async {
                            selectedReport = report
                            isReportReady = true

                            // 延迟一个runloop确保状态完全更新
                            DispatchQueue.main.async {
                                showingReportDetail = true
                                print("🔍 [HistoryGeneratedRecords] 状态设置完成，显示详情页")
                            }
                        }
                    }
                }
            }
        }
    }

    // MARK: - 空状态视图
    private func emptyStateView(type: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.text")
                .font(.system(size: 48))
                .foregroundColor(Color(red: 187/255, green: 187/255, blue: 187/255))

            Text("暂无\(type)")
                .font(.custom("PingFang SC", size: 18))
                .fontWeight(.medium)
                .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))

            Text("在历史记录页面生成\(type)后，会在这里显示")
                .font(.custom("PingFang SC", size: 14))
                .foregroundColor(Color(red: 187/255, green: 187/255, blue: 187/255))
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }
}

// MARK: - 历史报告行视图
struct HistoryReportRowView: View {
    let report: ReportEntity
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    // 报告标题
                    Text(report.title ?? "未知报告")
                        .font(.custom("PingFang SC", size: 16))
                        .fontWeight(.medium)
                        .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
                        .lineLimit(2)

                    Spacer()

                    // 箭头图标
                    Image("进入")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 16, height: 16)
                }

                HStack {
                    // 时间范围
                    Text(report.timeRange ?? "")
                        .font(.custom("PingFang SC", size: 14))
                        .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))

                    Spacer()

                    // 创建时间
                    Text(formatDate(report.createdAt))
                        .font(.custom("PingFang SC", size: 12))
                        .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm"
        return formatter.string(from: date)
    }
}

// MARK: - 报告详情视图
struct ReportDetailView: View {
    let report: ReportEntity
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    @State private var showingPDFExport = false
    @State private var isGeneratingPDF = false
    @State private var pdfShareItem: URL?
    @State private var showingPDFShareSheet = false
    @State private var showingPDFError = false
    @State private var pdfErrorMessage = ""

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // 报告信息
                    reportInfoSection

                    // 报告内容
                    reportContentSection
                }
                .padding(20)
            }
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 226/255, green: 255/255, blue: 232/255),
                        Color.white
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationTitle(report.type ?? "报告详情")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            print("🔍 [ReportDetailView] 页面即将显示")
            print("🔍 [ReportDetailView] 报告标题: \(report.title ?? "无标题")")
            print("🔍 [ReportDetailView] 报告类型: \(report.type ?? "无类型")")
            print("🔍 [ReportDetailView] 报告内容长度: \(report.content?.count ?? 0)")
            print("🔍 [ReportDetailView] 报告内容前100字符: \(String(report.content?.prefix(100) ?? "无内容"))")
            print("🔍 [ReportDetailView] 报告时间范围: \(report.timeRange ?? "无时间范围")")
            print("🔍 [ReportDetailView] 报告创建时间: \(report.createdAt?.description ?? "无创建时间")")
        }
        .onDisappear {
            print("🔍 [ReportDetailView] 页面即将消失")
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
        .alert("PDF导出", isPresented: $showingPDFExport) {
            Button("取消", role: .cancel) { }
            Button("导出") {
                Task {
                    await exportToPDF()
                }
            }
        } message: {
            Text("将身体档案导出为PDF文件，可直接打印给医生")
        }
        // PDF生成错误提示
        .alert("PDF导出失败", isPresented: $showingPDFError) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(pdfErrorMessage)
        }
        // PDF分享
        .sheet(isPresented: $showingPDFShareSheet) {
            if let pdfURL = pdfShareItem {
                ActivityViewController(activityItems: [pdfURL])
            }
        }
        // PDF生成加载指示器
        .overlay {
            if isGeneratingPDF {
                ZStack {
                    Color.black.opacity(0.3)
                        .ignoresSafeArea()

                    VStack(spacing: 16) {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(1.2)

                        Text("正在生成PDF...")
                            .font(.custom("PingFang SC", size: 16))
                            .foregroundColor(.white)
                    }
                    .padding(24)
                    .background(Color.black.opacity(0.7))
                    .cornerRadius(12)
                }
            }
        }
    }

    // MARK: - 报告信息区域
    private var reportInfoSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(report.title ?? "未知报告")
                .font(.custom("PingFang SC", size: 20))
                .fontWeight(.bold)
                .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))

            HStack {
                Text("时间范围：\(report.timeRange ?? "")")
                    .font(.custom("PingFang SC", size: 14))
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))

                Spacer()

                Text("生成时间：\(formatDate(report.createdAt))")
                    .font(.custom("PingFang SC", size: 14))
                    .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))
            }
        }
        .padding(16)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }

    // MARK: - 报告内容区域
    private var reportContentSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 使用Markdown渲染器显示内容
            MarkdownContentView(content: report.content ?? "内容为空")
        }
        .padding(16)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
    }

    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日 HH:mm"
        return formatter.string(from: date)
    }

    private func generateShareText() -> String {
        let title = report.title ?? "报告"
        let timeRange = report.timeRange ?? ""
        let content = report.content ?? ""
        let date = formatDate(report.createdAt)

        return """
        【\(title)】

        时间范围：\(timeRange)
        生成时间：\(date)

        \(content)

        ——来自身体日记App
        """
    }

    /// 导出PDF
    private func exportToPDF() async {
        isGeneratingPDF = true

        do {
            // 生成PDF数据
            guard let pdfData = PDFGeneratorService.shared.generateBodyArchivePDF(
                archiveContent: report.content ?? "",
                timeRange: report.timeRange ?? "",
                userInfo: nil // 可以传入用户信息
            ) else {
                throw PDFExportError.generationFailed
            }

            // 创建临时文件
            let tempURL = try await savePDFToTemporaryFile(data: pdfData)

            await MainActor.run {
                isGeneratingPDF = false
                pdfShareItem = tempURL
                showingPDFShareSheet = true
            }

        } catch {
            await MainActor.run {
                isGeneratingPDF = false
                pdfErrorMessage = "PDF生成失败：\(error.localizedDescription)"
                showingPDFError = true
            }
        }
    }

    /// 保存PDF到临时文件
    private func savePDFToTemporaryFile(data: Data) async throws -> URL {
        let tempDirectory = FileManager.default.temporaryDirectory
        let fileName = "身体档案_\(report.timeRange ?? "")_\(Date().timeIntervalSince1970).pdf"
        let tempURL = tempDirectory.appendingPathComponent(fileName)

        try data.write(to: tempURL)
        return tempURL
    }
}





#Preview {
    HistoryGeneratedRecordsView(dataViewModel: DataViewModel())
}
