//
//  ReminderEditView.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/21.
//

import SwiftUI

/// 提醒编辑界面
struct ReminderEditView: View {
    @ObservedObject var dataViewModel: DataViewModel
    @EnvironmentObject var notificationManager: NotificationManager

    let reminder: ReminderEntity?
    
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedTime = Date()
    @State private var reminderMessage = ""
    @State private var isActive = true
    @State private var isSaving = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    // 预设的提醒消息
    private let presetMessages = [
        "记录今天的身体状态",
        "今天感觉怎么样？",
        "别忘了记录身体变化",
        "关注身体健康，记录今日状态",
        "身体日记提醒：记录一下吧"
    ]
    
    var isEditing: Bool {
        reminder != nil
    }
    
    var navigationTitle: String {
        isEditing ? "编辑提醒" : "添加提醒"
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                ScrollView {
                    VStack(spacing: 24) {
                        // 时间选择器
                        timePickerSection
                        
                        // 消息输入
                        messageInputSection
                        
                        // 预设消息
                        presetMessagesSection
                        
                        // 激活状态
                        if isEditing {
                            activeToggleSection
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
                
                Spacer()
            }
            .navigationTitle(navigationTitle)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                    .foregroundColor(.secondary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveReminder()
                    }
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                    .disabled(reminderMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isSaving)
                }
            }
        }
        .alert("保存失败", isPresented: $showingError) {
            Button("确定") { }
        } message: {
            Text(errorMessage)
        }
        .onAppear {
            setupInitialValues()
        }
    }
    
    // MARK: - 时间选择器部分
    private var timePickerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("提醒时间")
                .font(.custom("PingFang SC", size: 16))
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            DatePicker(
                "选择时间",
                selection: $selectedTime,
                displayedComponents: .hourAndMinute
            )
            .datePickerStyle(.wheel)
            .labelsHidden()
            .background(Color.white)
            .cornerRadius(12)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 消息输入部分
    private var messageInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("提醒内容")
                .font(.custom("PingFang SC", size: 16))
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            TextField("输入提醒内容", text: $reminderMessage, axis: .vertical)
                .textFieldStyle(.plain)
                .padding()
                .background(Color.white)
                .cornerRadius(12)
                .lineLimit(3...6)
                .font(.custom("PingFang SC", size: 16))
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 预设消息部分
    private var presetMessagesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("快速选择")
                .font(.custom("PingFang SC", size: 16))
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                ForEach(presetMessages, id: \.self) { message in
                    Button(message) {
                        reminderMessage = message
                    }
                    .font(.custom("PingFang SC", size: 14))
                    .foregroundColor(reminderMessage == message ? .white : .primary)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        reminderMessage == message 
                        ? Color(red: 125/255, green: 175/255, blue: 106/255)
                        : Color.white
                    )
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color(.systemGray4), lineWidth: 1)
                            .opacity(reminderMessage == message ? 0 : 1)
                    )
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 激活状态部分
    private var activeToggleSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("启用提醒")
                        .font(.custom("PingFang SC", size: 16))
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text("关闭后将不会收到此提醒通知")
                        .font(.custom("PingFang SC", size: 14))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: $isActive)
                    .toggleStyle(SwitchToggleStyle(tint: Color(red: 125/255, green: 175/255, blue: 106/255)))
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 方法
    private func setupInitialValues() {
        if let reminder = reminder {
            // 编辑模式：使用现有数据
            selectedTime = reminder.time ?? Date()
            reminderMessage = reminder.message ?? ""
            isActive = reminder.isActive
        } else {
            // 新建模式：使用默认值
            let calendar = Calendar.current
            let now = Date()
            let components = calendar.dateComponents([.year, .month, .day], from: now)
            var timeComponents = DateComponents()
            timeComponents.year = components.year
            timeComponents.month = components.month
            timeComponents.day = components.day
            timeComponents.hour = 9 // 默认上午9点
            timeComponents.minute = 0
            
            selectedTime = calendar.date(from: timeComponents) ?? now
            reminderMessage = presetMessages.first ?? ""
            isActive = true
        }
    }
    
    private func saveReminder() {
        let message = reminderMessage.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !message.isEmpty else {
            errorMessage = "请输入提醒内容"
            showingError = true
            return
        }
        
        isSaving = true
        
        Task {
            if let existingReminder = reminder {
                // 更新现有提醒
                await updateExistingReminder(existingReminder, message: message)
            } else {
                // 创建新提醒
                await createNewReminder(message: message)
            }

            await MainActor.run {
                isSaving = false
                dismiss()
            }
        }
    }
    
    private func updateExistingReminder(_ reminder: ReminderEntity, message: String) async {
        // 使用DataViewModel的方法更新提醒
        await dataViewModel.updateReminder(reminder, time: selectedTime, message: message, isActive: isActive)

        // 更新通知
        await notificationManager.updateNotification(for: reminder)
    }
    
    private func createNewReminder(message: String) async {
        // 创建新提醒
        await dataViewModel.addReminder(time: selectedTime, message: message, isActive: isActive)
        
        // 如果激活状态，调度通知
        if isActive, let newReminder = dataViewModel.reminders.last {
            await notificationManager.scheduleNotification(for: newReminder)
        }
    }
}

// MARK: - 预览
struct ReminderEditView_Previews: PreviewProvider {
    static var previews: some View {
        ReminderEditViewPreview()
    }
}
