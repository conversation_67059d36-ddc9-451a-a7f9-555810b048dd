//
//  UserAgreementDebugView.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/21.
//

import SwiftUI

/// 用户协议调试视图 - 用于测试弹窗功能
struct UserAgreementDebugView: View {
    
    @StateObject private var userAgreementManager = UserAgreementManager.shared
    @State private var showUserAgreementPopup = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 状态信息
                statusSection
                
                // 操作按钮
                actionSection
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .navigationTitle("协议弹窗测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        .overlay(
            ZStack {
                if showUserAgreementPopup {
                    UserAgreementPopupView(
                        isPresented: $showUserAgreementPopup,
                        onAgree: {
                            userAgreementManager.agreeToTerms()
                            showUserAgreementPopup = false
                        },
                        onDisagree: {
                            userAgreementManager.disagreeToTerms()
                        }
                    )
                    .transition(.opacity)
                    .zIndex(1000)
                }
            }
            .animation(.easeInOut(duration: 0.3), value: showUserAgreementPopup)
        )
    }
    
    // MARK: - Status Section
    private var statusSection: some View {
        VStack(spacing: 16) {
            Text("当前状态")
                .font(.custom("PingFang SC", size: 18))
                .fontWeight(.bold)
                .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))
            
            VStack(spacing: 12) {
                StatusRow(
                    title: "是否需要显示弹窗",
                    value: userAgreementManager.shouldShowAgreementPopup() ? "是" : "否",
                    color: userAgreementManager.shouldShowAgreementPopup() ? .orange : .green
                )
                
                StatusRow(
                    title: "已同意协议",
                    value: userAgreementManager.hasAgreedToTerms ? "是" : "否",
                    color: userAgreementManager.hasAgreedToTerms ? .green : .red
                )
                
                if let date = userAgreementManager.getAgreementAcceptedDate() {
                    StatusRow(
                        title: "同意日期",
                        value: formatDate(date),
                        color: .blue
                    )
                }
                
                StatusRow(
                    title: "协议版本",
                    value: userAgreementManager.getCurrentAgreementVersion(),
                    color: .purple
                )
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 20)
            .background(Color(red: 248/255, green: 250/255, blue: 252/255))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Action Section
    private var actionSection: some View {
        VStack(spacing: 16) {
            Text("测试操作")
                .font(.custom("PingFang SC", size: 18))
                .fontWeight(.bold)
                .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))
            
            VStack(spacing: 12) {
                // 显示弹窗按钮
                Button(action: {
                    showUserAgreementPopup = true
                }) {
                    HStack {
                        Image(systemName: "doc.text.fill")
                        Text("显示协议弹窗")
                    }
                    .font(.custom("PingFang SC", size: 16))
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color(red: 125/255, green: 175/255, blue: 106/255))
                    .cornerRadius(12)
                }
                
                // 重置状态按钮
                Button(action: {
                    userAgreementManager.resetAgreementStatus()
                }) {
                    HStack {
                        Image(systemName: "arrow.clockwise")
                        Text("重置协议状态")
                    }
                    .font(.custom("PingFang SC", size: 16))
                    .fontWeight(.medium)
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(red: 125/255, green: 175/255, blue: 106/255), lineWidth: 2)
                    )
                }
                
                // 打印调试信息按钮
                #if DEBUG
                Button(action: {
                    userAgreementManager.printDebugInfo()
                }) {
                    HStack {
                        Image(systemName: "info.circle")
                        Text("打印调试信息")
                    }
                    .font(.custom("PingFang SC", size: 16))
                    .fontWeight(.medium)
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.gray, lineWidth: 1)
                    )
                }
                #endif
            }
        }
    }
    
    // MARK: - Helper Methods
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "yyyy年M月d日 HH:mm"
        return formatter.string(from: date)
    }
}

// MARK: - Status Row Component
struct StatusRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .font(.custom("PingFang SC", size: 14))
                .foregroundColor(Color(red: 85/255, green: 85/255, blue: 85/255))
            
            Spacer()
            
            Text(value)
                .font(.custom("PingFang SC", size: 14))
                .fontWeight(.medium)
                .foregroundColor(color)
        }
    }
}

// MARK: - Preview
#Preview {
    UserAgreementDebugView()
}
