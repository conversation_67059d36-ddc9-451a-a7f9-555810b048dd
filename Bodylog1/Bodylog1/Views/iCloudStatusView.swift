//
//  iCloudStatusView.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/21.
//

import SwiftUI
import CloudKit

/// iCloud状态显示视图
struct iCloudStatusView: View {
    @StateObject private var cloudKitManager = CloudKitManager.shared
    @State private var showingAlert = false

    var body: some View {
        HStack(spacing: 8) {
            // 状态图标
            statusIcon

            // 状态文本
            Text(statusText)
                .font(.caption)
                .foregroundColor(statusColor)

            // 刷新按钮
            if cloudKitManager.syncStatus.isError {
                Button(action: {
                    cloudKitManager.checkAccountStatus()
                    cloudKitManager.clearError()
                }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(backgroundColor)
        )
        .onTapGesture {
            if cloudKitManager.syncStatus.isError {
                showingAlert = true
            }
        }
        .alert("iCloud同步状态", isPresented: $showingAlert) {
            Button("确定", role: .cancel) { }
            if cloudKitManager.accountStatus != .available {
                Button("打开设置") {
                    openSettings()
                }
            }
        } message: {
            Text(detailedStatusMessage)
        }
        .onAppear {
            cloudKitManager.checkAccountStatus()
        }
    }

    // MARK: - Computed Properties

    private var statusIcon: some View {
        Group {
            switch cloudKitManager.syncStatus {
            case .syncing:
                ProgressView()
                    .scaleEffect(0.8)
            case .synced:
                Image(systemName: "icloud.fill")
                    .foregroundColor(.green)
            case .failed, .noAccount, .networkUnavailable:
                Image(systemName: "icloud.slash.fill")
                    .foregroundColor(.red)
            case .unknown:
                Image(systemName: "icloud")
                    .foregroundColor(.gray)
            }
        }
        .font(.caption)
    }

    private var statusText: String {
        switch cloudKitManager.syncStatus {
        case .syncing:
            return "同步中..."
        case .synced:
            if let lastSync = cloudKitManager.lastSyncDate {
                let formatter = DateFormatter()
                formatter.timeStyle = .short
                return "已同步 \(formatter.string(from: lastSync))"
            } else {
                return "已同步"
            }
        case .noAccount:
            return "未登录iCloud"
        case .networkUnavailable:
            return "网络不可用"
        case .failed:
            return "同步失败"
        case .unknown:
            return "检查中..."
        }
    }

    private var statusColor: Color {
        switch cloudKitManager.syncStatus {
        case .synced:
            return .green
        case .syncing:
            return .blue
        case .failed, .noAccount, .networkUnavailable:
            return .red
        case .unknown:
            return .gray
        }
    }

    private var backgroundColor: Color {
        switch cloudKitManager.syncStatus {
        case .synced:
            return .green.opacity(0.1)
        case .syncing:
            return .blue.opacity(0.1)
        case .failed, .noAccount, .networkUnavailable:
            return .red.opacity(0.1)
        case .unknown:
            return .gray.opacity(0.1)
        }
    }

    private var detailedStatusMessage: String {
        switch cloudKitManager.accountStatus {
        case .available:
            return cloudKitManager.syncStatus.description
        case .noAccount:
            return "您尚未登录iCloud账户。请在设置中登录iCloud以启用数据同步功能。"
        case .restricted:
            return "iCloud功能受到限制。请检查您的设备设置和家长控制设置。"
        case .couldNotDetermine:
            return "无法确定iCloud状态。请检查网络连接并重试。"
        case .temporarilyUnavailable:
            return "iCloud服务暂时不可用。请稍后重试。"
        @unknown default:
            return "未知的iCloud状态。"
        }
    }

    // MARK: - Private Methods

    private func openSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}

// MARK: - Preview
struct iCloudStatusView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            iCloudStatusView()

            // 不同状态的预览
            HStack {
                Image(systemName: "icloud.fill")
                    .foregroundColor(.green)
                Text("已同步")
                    .font(.caption)
                    .foregroundColor(.green)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(.green.opacity(0.1))
            )
        }
        .padding()
    }
}