//
//  BodyArchiveChartsView.swift
//  Bodylog1
//
//  Created by rainkygong on 2025/7/20.
//

import SwiftUI
import Charts

// MARK: - 身体档案图表视图

struct BodyArchiveChartsView: View {
    let records: [RecordEntity]

    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 症状趋势图
                SymptomTrendChartView(records: records)

                // 症状频率饼图
                SymptomFrequencyChartView(records: records)

                // 饮食与不适关联图
                FoodDiscomfortChartView(records: records)

                // 作息与症状关联图
                SleepSymptomChartView(records: records)
            }
            .padding(.horizontal, 16)
        }
    }
}

// MARK: - 症状趋势图

struct SymptomTrendChartView: View {
    let records: [RecordEntity]

    private var trendData: [SymptomTrendData] {
        ChartDataProcessor.extractSymptomTrends(from: records)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("症状趋势图")
                .font(.headline)
                .foregroundColor(.primary)

            if !trendData.isEmpty {
                Chart {
                    ForEach(trendData, id: \.id) { data in
                        BarMark(
                            x: .value("日期", data.date),
                            y: .value("次数", data.count)
                        )
                        .foregroundStyle(by: .value("症状", data.symptom))
                    }
                }
                .frame(height: 200)
                .chartXAxis {
                    AxisMarks(values: .stride(by: .day, count: 2)) { value in
                        AxisGridLine()
                        AxisValueLabel(format: .dateTime.month().day())
                    }
                }
                .chartYAxis {
                    AxisMarks { value in
                        AxisGridLine()
                        AxisValueLabel()
                    }
                }
                .chartLegend(position: .bottom, alignment: .center)
            } else {
                Text("暂无症状数据")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, minHeight: 100)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

// MARK: - 症状频率饼图

struct SymptomFrequencyChartView: View {
    let records: [RecordEntity]

    private var frequencyData: [SymptomFrequency] {
        ChartDataProcessor.calculateSymptomFrequency(from: records)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("症状频率分布")
                .font(.headline)
                .foregroundColor(.primary)

            if !frequencyData.isEmpty {
                // 使用简单的条形图代替饼图（iOS 16兼容）
                Chart {
                    ForEach(frequencyData.prefix(8), id: \.id) { data in
                        BarMark(
                            x: .value("症状", data.symptom),
                            y: .value("频率", data.count)
                        )
                        .foregroundStyle(by: .value("症状", data.symptom))
                    }
                }
                .frame(height: 200)
                .chartXAxis {
                    AxisMarks { value in
                        AxisValueLabel()
                    }
                }
                .chartLegend(position: .bottom, alignment: .center)
            } else {
                Text("暂无症状数据")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, minHeight: 100)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

// MARK: - 饮食与不适关联图

struct FoodDiscomfortChartView: View {
    let records: [RecordEntity]

    private var foodDiscomfortData: [FoodDiscomfortData] {
        ChartDataProcessor.analyzeFoodDiscomfortCorrelation(from: records)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("饮食与不适关联")
                .font(.headline)
                .foregroundColor(.primary)

            if !foodDiscomfortData.isEmpty {
                Chart {
                    ForEach(foodDiscomfortData, id: \.id) { data in
                        PointMark(
                            x: .value("时间", data.date),
                            y: .value("有不适", data.hasDiscomfort ? 1 : 0)
                        )
                        .foregroundStyle(by: .value("食物", data.foodEvent))
                        .symbol(by: .value("食物", data.foodEvent))
                    }
                }
                .frame(height: 150)
                .chartXAxis {
                    AxisMarks(values: .stride(by: .day, count: 1)) { value in
                        AxisGridLine()
                        AxisValueLabel(format: .dateTime.month().day())
                    }
                }
                .chartYAxis {
                    AxisMarks { value in
                        AxisGridLine()
                        AxisValueLabel()
                    }
                }
                .chartLegend(position: .bottom, alignment: .center)
            } else {
                Text("暂无饮食数据")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, minHeight: 100)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

// MARK: - 作息与症状关联图

struct SleepSymptomChartView: View {
    let records: [RecordEntity]

    private var sleepSymptomData: [SleepSymptomData] {
        ChartDataProcessor.analyzeSleepSymptomCorrelation(from: records)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("作息与症状关联")
                .font(.headline)
                .foregroundColor(.primary)

            if !sleepSymptomData.isEmpty {
                Chart {
                    ForEach(Array(sleepSymptomData.enumerated()), id: \.offset) { index, data in
                        BarMark(
                            x: .value("睡眠质量", data.sleepQuality),
                            y: .value("症状次数", data.symptomCount)
                        )
                        .foregroundStyle(by: .value("睡眠质量", data.sleepQuality))
                    }
                }
                .frame(height: 150)
                .chartXAxis {
                    AxisMarks { value in
                        AxisGridLine()
                        AxisValueLabel()
                    }
                }
                .chartYAxis {
                    AxisMarks { value in
                        AxisGridLine()
                        AxisValueLabel()
                    }
                }
                .chartLegend(position: .bottom, alignment: .center)
            } else {
                Text("暂无作息数据")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, minHeight: 100)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

// MARK: - 预览

#Preview {
    BodyArchiveChartsView(records: [])
}