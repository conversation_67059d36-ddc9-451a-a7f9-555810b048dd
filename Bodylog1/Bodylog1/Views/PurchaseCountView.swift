//
//  PurchaseCountView.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/21.
//

import SwiftUI
import RevenueCat

/// 购买次数页面
struct PurchaseCountView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var showUserAgreement = false
    @State private var showPrivacyPolicy = false
    @State private var showPurchaseAlert = false
    @State private var selectedProduct: PurchaseProduct?

    // RevenueCat 购买服务
    @StateObject private var purchaseService: PurchaseService

    // 初始化方法
    init(dataViewModel: DataViewModel) {
        self._purchaseService = StateObject(wrappedValue: PurchaseService(dataViewModel: dataViewModel))
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景渐变
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 226/255, green: 255/255, blue: 232/255),
                        Color.white
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // 加载状态或产品选项卡
                        if purchaseService.isLoading {
                            loadingSection
                        } else if purchaseService.availablePackages.isEmpty {
                            noProductsSection
                        } else {
                            revenueCatProductCardsSection
                        }

                        // 购买说明
                        purchaseNotesSection

                        Spacer(minLength: 20)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                }
            }
            .navigationTitle("购买次数")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                }
            }
        }
        .sheet(isPresented: $showUserAgreement) {
            UserAgreementView()
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            PrivacyPolicyView()
        }
        .alert("购买提示", isPresented: $showPurchaseAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            if let product = selectedProduct {
                Text("您选择了购买\(product.count)次调用AI次数，价格¥\(product.price)。内购功能将在后续版本中实现。")
            }
        }
        // RevenueCat 购买状态处理
        .onChange(of: purchaseService.purchaseState) { state in
            handlePurchaseStateChange(state)
        }
        .onAppear {
            // 页面出现时刷新产品信息
            Task {
                await purchaseService.loadOfferings()
            }
        }
    }
    
    // MARK: - 产品卡片区域
    private var productCardsSection: some View {
        VStack(spacing: 16) {
            ForEach(PurchaseProduct.allProducts, id: \.id) { product in
                ProductCard(product: product) {
                    selectedProduct = product
                    showPurchaseAlert = true
                }
            }
        }
    }
    
    // MARK: - 购买说明区域
    private var purchaseNotesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("购买说明")
                .font(.custom("PingFang SC", size: 18))
                .fontWeight(.semibold)
                .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
            
            VStack(alignment: .leading, spacing: 12) {
                PurchaseNoteItem(text: "购买的次数包永久有效")
                PurchaseNoteItem(text: "每次调用AI需要消耗一次")
                PurchaseNoteItem(text: "AI生成的分析报告和身体档案历史记录可在个人中心查看")
                
                // 包含链接的说明
                HStack(alignment: .top, spacing: 8) {
                    Circle()
                        .fill(Color(red: 125/255, green: 175/255, blue: 106/255))
                        .frame(width: 6, height: 6)
                        .padding(.top, 8)
                    
                    HStack(spacing: 0) {
                        Text("购买前请查看")
                            .font(.custom("PingFang SC", size: 14))
                            .foregroundColor(Color(red: 102/255, green: 102/255, blue: 102/255))
                        
                        Button("《用户协议》") {
                            showUserAgreement = true
                        }
                        .font(.custom("PingFang SC", size: 14))
                        .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                        
                        Text("和")
                            .font(.custom("PingFang SC", size: 14))
                            .foregroundColor(Color(red: 102/255, green: 102/255, blue: 102/255))
                        
                        Button("《隐私政策》") {
                            showPrivacyPolicy = true
                        }
                        .font(.custom("PingFang SC", size: 14))
                        .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                    }
                }
            }
        }
        .padding(20)
        .background(Color.clear)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }

    // MARK: - RevenueCat 产品卡片区域
    private var revenueCatProductCardsSection: some View {
        VStack(spacing: 16) {
            ForEach(purchaseService.availablePackages, id: \.identifier) { package in
                RevenueCatProductCard(package: package) {
                    Task {
                        let success = await purchaseService.purchase(package: package)
                        if success {
                            // 购买成功，可以在这里添加额外的处理逻辑
                            print("✅ 购买成功: \(package.storeProduct.localizedTitle)")
                        }
                    }
                }
            }
        }
    }

    // MARK: - 加载状态区域
    private var loadingSection: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(Color(red: 125/255, green: 175/255, blue: 106/255))

            Text("正在加载产品信息...")
                .font(.custom("PingFang SC", size: 16))
                .foregroundColor(Color(red: 102/255, green: 102/255, blue: 102/255))
        }
        .frame(height: 120)
    }

    // MARK: - 无产品区域
    private var noProductsSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 40))
                .foregroundColor(Color(red: 255/255, green: 193/255, blue: 7/255))

            Text("暂无可用产品")
                .font(.custom("PingFang SC", size: 18))
                .fontWeight(.medium)
                .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))

            Text("请稍后重试或联系客服")
                .font(.custom("PingFang SC", size: 14))
                .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))

            Button("重新加载") {
                Task {
                    await purchaseService.loadOfferings()
                }
            }
            .font(.custom("PingFang SC", size: 16))
            .fontWeight(.medium)
            .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
            .padding(.top, 8)
        }
        .frame(height: 160)
    }

    // MARK: - 购买状态处理
    private func handlePurchaseStateChange(_ state: PurchaseState) {
        switch state {
        case .success(let message):
            // 显示成功消息
            showSuccessAlert(message: message)
        case .failed(let message):
            // 显示错误消息
            showErrorAlert(message: message)
        case .idle, .loading:
            break
        }
    }

    private func showSuccessAlert(message: String) {
        let alert = UIAlertController(title: "购买成功", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            purchaseService.resetPurchaseState()
        })

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(alert, animated: true)
        }
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "购买失败", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            purchaseService.resetPurchaseState()
        })

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(alert, animated: true)
        }
    }
}

// MARK: - RevenueCat 产品卡片组件
struct RevenueCatProductCard: View {
    let package: Package
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // 左侧图标
                Image(systemName: "sparkles")
                    .font(.system(size: 24))
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                    .frame(width: 40, height: 40)
                    .background(Color(red: 227/255, green: 240/255, blue: 224/255))
                    .cornerRadius(20)

                // 中间内容
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(package.callCount)次")
                        .font(.custom("PingFang SC", size: 16))
                        .fontWeight(.medium)
                        .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))

                    Text("调用AI次数")
                        .font(.custom("PingFang SC", size: 12))
                        .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))
                }

                Spacer()

                // 右侧价格
                VStack(alignment: .trailing, spacing: 2) {
                    Text(package.formattedPrice)
                        .font(.custom("PingFang SC", size: 20))
                        .fontWeight(.bold)
                        .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))

                    Text("一次性购买")
                        .font(.custom("PingFang SC", size: 10))
                        .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))
                }
            }
            .padding(20)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(package.callCount == 0) // 如果无法识别产品则禁用
    }
}

// MARK: - 产品数据模型
struct PurchaseProduct {
    let id = UUID()
    let count: Int
    let price: Int
    
    static let allProducts = [
        PurchaseProduct(count: 20, price: 18),
        PurchaseProduct(count: 50, price: 38)
    ]
}

// MARK: - 产品卡片组件
struct ProductCard: View {
    let product: PurchaseProduct
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // 左侧图标
                Image(systemName: "sparkles")
                    .font(.system(size: 24))
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                    .frame(width: 40, height: 40)
                    .background(Color(red: 227/255, green: 240/255, blue: 224/255))
                    .cornerRadius(20)
                
                // 中间内容
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(product.count)次")
                        .font(.custom("PingFang SC", size: 16))
                        .fontWeight(.medium)
                        .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
                    
                    Text("调用AI次数")
                        .font(.custom("PingFang SC", size: 12))
                        .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))
                }
                
                Spacer()
                
                // 右侧价格
                VStack(alignment: .trailing, spacing: 2) {
                    Text("¥\(product.price)")
                        .font(.custom("PingFang SC", size: 20))
                        .fontWeight(.bold)
                        .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                    
                    Text("一次性购买")
                        .font(.custom("PingFang SC", size: 10))
                        .foregroundColor(Color(red: 153/255, green: 153/255, blue: 153/255))
                }
            }
            .padding(20)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 购买说明条目组件
struct PurchaseNoteItem: View {
    let text: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Circle()
                .fill(Color(red: 125/255, green: 175/255, blue: 106/255))
                .frame(width: 6, height: 6)
                .padding(.top, 8)
            
            Text(text)
                .font(.custom("PingFang SC", size: 14))
                .foregroundColor(Color(red: 102/255, green: 102/255, blue: 102/255))
                .lineSpacing(2)
        }
    }
}

#Preview {
    PurchaseCountView(dataViewModel: DataViewModel.preview())
}
