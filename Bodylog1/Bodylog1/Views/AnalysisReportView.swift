//
//  AnalysisReportView.swift
//  Bodylog1
//
//  Created by Augment Agent on 2025/7/20.
//

import SwiftUI

/// 分析报告显示页面
struct AnalysisReportView: View {
    let reportContent: String
    let timeRange: String
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景渐变
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 248/255, green: 252/255, blue: 249/255),
                        Color.white
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 0) {
                        // 报告头部
                        VStack(spacing: 16) {
                            // 标题
                            Text("健康分析报告")
                                .font(.custom("PingFang SC", size: 24))
                                .fontWeight(.bold)
                                .foregroundColor(Color(red: 34/255, green: 34/255, blue: 34/255))
                            
                            // 时间范围
                            Text(timeRange)
                                .font(.custom("PingFang SC", size: 16))
                                .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    Capsule()
                                        .fill(Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.1))
                                )
                            
                            // 生成时间
                            Text("生成时间: \(formattedCurrentTime)")
                                .font(.custom("PingFang SC", size: 14))
                                .foregroundColor(.gray)
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 20)
                        .padding(.bottom, 24)
                        
                        // 报告内容
                        VStack(alignment: .leading, spacing: 0) {
                            MarkdownContentView(content: reportContent)
                                .padding(.horizontal, 20)
                                .padding(.vertical, 24)
                        }
                        .background(Color.white)
                        .cornerRadius(16)
                        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
                        .padding(.horizontal, 16)
                        
                        Spacer(minLength: 100)
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                    .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingShareSheet = true
                    }) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
    }
    
    /// 格式化当前时间
    private var formattedCurrentTime: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: Date())
    }
    
    /// 生成分享文本
    private func generateShareText() -> String {
        return """
        健康分析报告
        时间范围: \(timeRange)
        生成时间: \(formattedCurrentTime)
        
        \(reportContent)
        
        ——来自身体日记App
        """
    }
}

/// Markdown内容视图（简化版）
struct MarkdownContentView: View {
    let content: String

    var body: some View {
        let sections = parseMarkdownContent()

        VStack(alignment: .leading, spacing: 16) {
            if sections.isEmpty {
                // 如果没有解析出内容，显示原始文本
                VStack(alignment: .leading, spacing: 12) {
                    Text("内容加载")
                        .font(.custom("PingFang SC", size: 18))
                        .fontWeight(.medium)
                        .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))

                    if content.isEmpty {
                        Text("暂无内容")
                            .font(.custom("PingFang SC", size: 16))
                            .foregroundColor(.secondary)
                    } else {
                        Text(content)
                            .font(.custom("PingFang SC", size: 16))
                            .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
                            .lineSpacing(4)
                    }
                }
            } else {
                ForEach(sections, id: \.id) { section in
                    MarkdownSectionView(section: section)
                }
            }
        }
        .onAppear {
            print("🔍 [MarkdownContentView] 开始渲染内容")
            print("🔍 [MarkdownContentView] 原始内容长度: \(content.count)")
            print("🔍 [MarkdownContentView] 解析后段落数量: \(sections.count)")
            print("🔍 [MarkdownContentView] 原始内容前200字符: \(String(content.prefix(200)))")

            for (index, section) in sections.enumerated() {
                print("🔍 [MarkdownContentView] 段落\(index): 标题='\(section.title)', 级别=\(section.level), 内容行数=\(section.content.count)")
            }
        }
    }
    
    /// 解析Markdown内容
    private func parseMarkdownContent() -> [MarkdownSection] {
        print("🔍 [MarkdownContentView] 开始解析Markdown内容")

        // 首先清理内容
        let cleanedContent = cleanupContent(content)
        print("🔍 [MarkdownContentView] 清理后内容长度: \(cleanedContent.count)")

        let lines = cleanedContent.components(separatedBy: .newlines)
        print("🔍 [MarkdownContentView] 分割后行数: \(lines.count)")

        var sections: [MarkdownSection] = []
        var currentSection: MarkdownSection?

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)

            if trimmedLine.isEmpty {
                continue
            }

            // 检查是否是标题
            if trimmedLine.hasPrefix("#") {
                // 保存当前section
                if let section = currentSection {
                    sections.append(section)
                }

                // 创建新section
                let level = trimmedLine.prefix(while: { $0 == "#" }).count
                let title = String(trimmedLine.dropFirst(level)).trimmingCharacters(in: .whitespaces)
                currentSection = MarkdownSection(
                    id: UUID(),
                    title: title,
                    level: level,
                    content: []
                )
            } else {
                // 添加内容到当前section
                if currentSection == nil {
                    currentSection = MarkdownSection(
                        id: UUID(),
                        title: "",
                        level: 0,
                        content: []
                    )
                }
                currentSection?.content.append(trimmedLine)
            }
        }

        // 添加最后一个section
        if let section = currentSection {
            sections.append(section)
        }

        print("🔍 [MarkdownContentView] 解析完成，共生成 \(sections.count) 个段落")

        if sections.isEmpty {
            print("❌ [MarkdownContentView] 警告：没有解析出任何段落！")
            print("❌ [MarkdownContentView] 原始内容: '\(content)'")
            print("❌ [MarkdownContentView] 清理后内容: '\(cleanedContent)'")
        }

        return sections
    }

    /// 清理内容格式
    private func cleanupContent(_ content: String) -> String {
        var cleaned = content

        // 移除多余的星号和特殊符号
        cleaned = cleaned.replacingOccurrences(of: "\\*\\*([^*]+)\\*\\*", with: "$1", options: .regularExpression)
        cleaned = cleaned.replacingOccurrences(of: "\\*([^*]+)\\*", with: "$1", options: .regularExpression)

        // 清理表格分隔符
        cleaned = cleaned.replacingOccurrences(of: "\\|[-\\s]+\\|", with: "", options: .regularExpression)
        cleaned = cleaned.replacingOccurrences(of: "[-]{3,}", with: "", options: .regularExpression)

        // 处理表格行
        let lines = cleaned.components(separatedBy: .newlines)
        var processedLines: [String] = []

        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespaces)

            // 跳过空行和分隔符行
            if trimmed.isEmpty || trimmed.allSatisfy({ $0 == "-" || $0 == "|" || $0.isWhitespace }) {
                continue
            }

            // 处理表格行
            if trimmed.contains("|") {
                let cells = trimmed.components(separatedBy: "|")
                    .map { $0.trimmingCharacters(in: .whitespaces) }
                    .filter { !$0.isEmpty }

                if !cells.isEmpty {
                    processedLines.append(cells.joined(separator: " | "))
                }
            } else {
                processedLines.append(trimmed)
            }
        }

        return processedLines.joined(separator: "\n")
    }
}

/// Markdown段落结构
struct MarkdownSection {
    let id: UUID
    let title: String
    let level: Int
    var content: [String]
}

/// Markdown段落视图
struct MarkdownSectionView: View {
    let section: MarkdownSection
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题
            if !section.title.isEmpty {
                Text(section.title)
                    .font(titleFont)
                    .fontWeight(.bold)
                    .foregroundColor(titleColor)
                    .padding(.bottom, 4)
            }
            
            // 内容
            VStack(alignment: .leading, spacing: 8) {
                ForEach(section.content, id: \.self) { line in
                    ContentLineView(line: line)
                }
            }
        }
    }
    
    /// 标题字体
    private var titleFont: Font {
        switch section.level {
        case 1:
            return .custom("PingFang SC", size: 22)
        case 2:
            return .custom("PingFang SC", size: 20)
        case 3:
            return .custom("PingFang SC", size: 18)
        default:
            return .custom("PingFang SC", size: 16)
        }
    }
    
    /// 标题颜色
    private var titleColor: Color {
        switch section.level {
        case 1:
            return Color(red: 34/255, green: 34/255, blue: 34/255)
        case 2:
            return Color(red: 125/255, green: 175/255, blue: 106/255)
        default:
            return Color(red: 68/255, green: 68/255, blue: 68/255)
        }
    }
    
}

/// 内容行视图
struct ContentLineView: View {
    let line: String

    var body: some View {
        let formattedLine = formatContentLine(line)

        if isTableRow(line) {
            // 表格行
            TableRowView(content: formattedLine)
        } else if isListItem(line) {
            // 列表项
            ListItemView(content: formattedLine)
        } else if isHighlightedText(line) {
            // 重要信息
            HighlightedTextView(content: formattedLine)
        } else {
            // 普通文本
            Text(formattedLine)
                .font(.custom("PingFang SC", size: 16))
                .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
                .lineSpacing(4)
                .fixedSize(horizontal: false, vertical: true)
        }
    }

    private func isTableRow(_ line: String) -> Bool {
        return line.contains(" | ") || line.contains("  ·  ")
    }

    private func isListItem(_ line: String) -> Bool {
        return line.hasPrefix("- ") || line.hasPrefix("• ") || line.hasPrefix("> ") || line.hasPrefix("▶ ")
    }

    private func isHighlightedText(_ line: String) -> Bool {
        return line.contains("**") || line.contains("风险") || line.contains("建议") || line.contains("注意")
    }

    private func formatContentLine(_ line: String) -> String {
        var formatted = line

        // 处理列表项
        if formatted.hasPrefix("- ") {
            formatted = "• " + String(formatted.dropFirst(2))
        } else if formatted.hasPrefix("> ") {
            formatted = "▶ " + String(formatted.dropFirst(2))
        }

        // 处理表格分隔符
        if formatted.contains(" | ") {
            formatted = formatted.replacingOccurrences(of: " | ", with: "  ·  ")
        }

        // 移除HTML标签
        formatted = formatted.replacingOccurrences(of: "<br>", with: "\n")
        formatted = formatted.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)

        // 移除多余的星号
        formatted = formatted.replacingOccurrences(of: "\\*\\*([^*]+)\\*\\*", with: "$1", options: .regularExpression)
        formatted = formatted.replacingOccurrences(of: "\\*([^*]+)\\*", with: "$1", options: .regularExpression)

        // 清理多余的空格
        formatted = formatted.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
        formatted = formatted.trimmingCharacters(in: .whitespaces)

        return formatted
    }
}

/// 表格行视图
struct TableRowView: View {
    let content: String

    var body: some View {
        HStack {
            Text(content)
                .font(.custom("PingFang SC", size: 15))
                .foregroundColor(Color(red: 68/255, green: 68/255, blue: 68/255))
                .lineSpacing(3)
                .fixedSize(horizontal: false, vertical: true)
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(red: 248/255, green: 250/255, blue: 252/255))
        )
    }
}

/// 列表项视图
struct ListItemView: View {
    let content: String

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Text(content)
                .font(.custom("PingFang SC", size: 16))
                .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
                .lineSpacing(4)
                .fixedSize(horizontal: false, vertical: true)
            Spacer()
        }
        .padding(.leading, 8)
    }
}

/// 高亮文本视图
struct HighlightedTextView: View {
    let content: String

    var body: some View {
        Text(content)
            .font(.custom("PingFang SC", size: 16))
            .fontWeight(.medium)
            .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
            .lineSpacing(4)
            .fixedSize(horizontal: false, vertical: true)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.1))
            )
    }
}

/// 分享功能
struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}
