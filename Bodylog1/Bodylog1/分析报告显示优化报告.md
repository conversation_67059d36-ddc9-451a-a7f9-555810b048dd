# 分析报告显示格式优化报告

## 问题描述
从用户反馈的截图可以看到，生成的分析报告存在格式问题：
- 出现乱码和不必要的符号（如 `**`、`|`、`---` 等）
- 表格格式混乱，难以阅读
- 内容排版不清晰，用户体验差

## 优化方案

### 1. 改进AI Prompt模板

**文件**: `Bodylog1/分析报告prompt.md`

**新增格式要求**:
```markdown
### 重要格式要求：
- 使用简洁的格式，避免复杂的表格和特殊符号
- 避免使用 **粗体** 标记，直接使用清晰的文字表达
- 不要使用表格分隔符（如 | --- |）
- 内容分段清晰，每段不超过3行
- 使用简单的列表格式，避免复杂的嵌套

### 格式示例：
## 概览总结
根据您近期的身体记录，发现了一些需要关注的健康信号...

## 症状趋势分析
- 胃部不适：出现频率较高，主要集中在晚餐后
- 睡眠质量：整体偏差，入睡困难现象明显

## 风险与原因
胃部不适可能与以下因素相关：
- 饮食习惯：经常食用辛辣刺激性食物
- 用餐时间：晚餐时间过晚，影响消化

风险等级：中等
```

### 2. 优化报告显示组件

**文件**: `Bodylog1/Views/AnalysisReportView.swift`

#### 2.1 内容清理功能
```swift
/// 清理内容格式
private func cleanupContent(_ content: String) -> String {
    var cleaned = content
    
    // 移除多余的星号和特殊符号
    cleaned = cleaned.replacingOccurrences(of: "\\*\\*([^*]+)\\*\\*", with: "$1", options: .regularExpression)
    cleaned = cleaned.replacingOccurrences(of: "\\*([^*]+)\\*", with: "$1", options: .regularExpression)
    
    // 清理表格分隔符
    cleaned = cleaned.replacingOccurrences(of: "\\|[-\\s]+\\|", with: "", options: .regularExpression)
    cleaned = cleaned.replacingOccurrences(of: "[-]{3,}", with: "", options: .regularExpression)
    
    // 处理表格行
    // 将 | 分隔的内容转换为更清晰的格式
    
    return processedLines.joined(separator: "\n")
}
```

#### 2.2 智能内容识别
```swift
struct ContentLineView: View {
    let line: String
    
    var body: some View {
        if isTableRow(line) {
            TableRowView(content: formattedLine)
        } else if isListItem(line) {
            ListItemView(content: formattedLine)
        } else if isHighlightedText(line) {
            HighlightedTextView(content: formattedLine)
        } else {
            // 普通文本
            Text(formattedLine)
        }
    }
}
```

#### 2.3 专用视图组件

**表格行视图**:
```swift
struct TableRowView: View {
    let content: String
    
    var body: some View {
        HStack {
            Text(content)
                .font(.custom("PingFang SC", size: 15))
                .foregroundColor(Color(red: 68/255, green: 68/255, blue: 68/255))
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(red: 248/255, green: 250/255, blue: 252/255))
        )
    }
}
```

**列表项视图**:
```swift
struct ListItemView: View {
    let content: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Text(content)
                .font(.custom("PingFang SC", size: 16))
                .foregroundColor(Color(red: 51/255, green: 51/255, blue: 51/255))
            Spacer()
        }
        .padding(.leading, 8)
    }
}
```

**高亮文本视图**:
```swift
struct HighlightedTextView: View {
    let content: String
    
    var body: some View {
        Text(content)
            .font(.custom("PingFang SC", size: 16))
            .fontWeight(.medium)
            .foregroundColor(Color(red: 125/255, green: 175/255, blue: 106/255))
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color(red: 125/255, green: 175/255, blue: 106/255).opacity(0.1))
            )
    }
}
```

### 3. 格式化处理逻辑

#### 3.1 符号清理
- 移除 `**粗体**` 标记
- 清理表格分隔符 `| --- |`
- 处理HTML标签 `<br>`
- 统一列表符号为 `•`

#### 3.2 内容分类
- **表格行**: 包含 `|` 或 `·` 分隔符的内容
- **列表项**: 以 `-`、`•`、`>` 开头的内容
- **重要信息**: 包含"风险"、"建议"、"注意"等关键词
- **普通文本**: 其他常规内容

#### 3.3 视觉优化
- 表格行使用浅灰色背景
- 列表项增加缩进
- 重要信息使用绿色高亮
- 统一字体和间距

## 优化效果

### ✅ 解决的问题
1. **格式混乱** → 清晰的分段显示
2. **符号乱码** → 自动清理特殊符号
3. **表格难读** → 专用表格行视图
4. **内容单调** → 智能内容分类和样式

### 🎯 用户体验改进
- **可读性提升**: 清晰的视觉层次
- **信息突出**: 重要内容自动高亮
- **格式统一**: 一致的排版风格
- **视觉美观**: 现代化的UI设计

### 📊 技术改进
- 智能内容解析和分类
- 正则表达式清理格式
- 组件化的显示架构
- 响应式布局设计

## 兼容性说明
- ✅ 兼容iOS 16.6+
- ✅ 使用当前大模型：Claude Sonnet 4
- ✅ 保持现有功能完整性
- ✅ 向后兼容所有报告格式

## 测试建议

### 1. 内容格式测试
- 测试包含表格的报告显示
- 验证列表项格式是否正确
- 检查特殊符号清理效果

### 2. 视觉效果测试
- 确认高亮显示是否正常
- 验证字体和颜色一致性
- 测试不同长度内容的显示

### 3. 兼容性测试
- 测试旧版本报告的显示
- 验证各种格式内容的处理
- 确认分享功能正常工作

## 后续优化建议
1. 添加更多内容类型识别
2. 支持图表和数据可视化
3. 增加字体大小调节功能
4. 考虑深色模式适配
