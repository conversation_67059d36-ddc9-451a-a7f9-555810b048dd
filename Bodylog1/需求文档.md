《身体日记 - PRD V1.0》
1. 产品概述
1.1 产品背景
用户在日常生活中经常出现头痛、胃痛、腹泻等身体不适情况，但大多数人忽视了这些身体信号。本应用旨在帮助用户记录这些身体状态，并基于 AI 大模型提供分析报告，帮助用户识别潜在健康风险。
1.2 核心价值
* 记录：用户可以通过语音或手动快速记录身体事件。
* 分析：AI 大模型基于用户数据生成分析报告。
* 档案：可导出“身体档案”，就医时作为参考。
* 数据安全：本地存储 + iCloud 同步，防止数据丢失。

2. 目标用户
* 注重健康管理的普通用户
* 有轻微慢性病或亚健康群体
* 经常忽略身体信号的人群

3. 核心功能
3.1 历史记录页面
* 日历视图（完整显示当月日期）
* 日期下方显示小圆点（表示当天有记录）
* 点击日期 → 展示当日所有记录（按时间倒序）
* 操作按钮：
    * [生成分析报告]
    * [生成身体档案]
3.2 快速记录页面
* 语音输入（长按按钮录音 → 转文字 → 确认）
* 手动输入
* 补记录功能（可选择补记时间）
3.3 个人中心页面
* 用户信息展示
* 剩余调用次数 + 购买入口（内购）
* 历史生成记录（分析报告 & 身体档案，分段显示）
* 提醒设置
* 系统设置（退出、删除账号、隐私政策、用户协议、联系我们）

4. 数据存储与同步
* CoreData 存储本地数据
* iCloud 同步（NSPersistentCloudKitContainer）
* 无用户注册，绑定 Apple ID 自动同步
* 用户未开启 iCloud → 本地使用 + 提示开启

5. AI 调用逻辑
* 生成分析报告：
    * 用户选择时间范围 → CoreData 查询记录 → 拼接 prompt → 调用 API → 存储结果
* 生成身体档案：
    * 用户选择时间范围 → 汇总所有记录 + 生成图表（趋势图、饮食对比、作息相关性） → 存储结果
* 每次调用 消耗一次调用次数（内购购买次数）

6. 商业模式
* 首次注册赠送 10 次调用
* 购买调用次数：20 次 / 18 元    50次/38元（内购）
* 后续版本可能增加订阅、增值服务（例如健康建议、医生解读）

7. 提醒功能
* 用户可设置多个提醒（时间 + 文案）
* 使用 UNUserNotificationCenter 实现本地通知

8. 技术架构
UI 层：SwiftUI + MVVM
数据层：CoreData + iCloud（NSPersistentCloudKitContainer）
AI 调用：后端 API （调用Deepseek）
内购：RevenueCat + StoreKit2
扩展点：
* HealthKitManager（第二版实现）
* 图表：Swift Charts
* 日历：SwiftUICalendar
架构图：
mermaid
复制编辑
graph TD
UI[SwiftUI界面层] --> VM[ViewModel(MVVM)]
VM --> CoreData[CoreData + CloudKit]
VM --> LocalNotifications[本地通知]
VM --> InApp[RevenueCat]
VM --> AI[大模型API调用]
VM --> HealthKitManager[未来扩展]

9. 页面信息架构
Tab栏：
* 历史记录（日历 + 当日列表 + 生成报告/档案）
* 快速记录（语音/手动 + 补记）
* 个人中心（性别选择、年龄、剩余次数、购买、提醒设置、历史报告、系统设置）